import 'package:flutter/material.dart';

import 'dart:convert';

import 'package:octasync_client/api/role_auth_management.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';
import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_mgmt/create_group_dialog.dart';

import 'package:octasync_client/views/admin/role_auth_management/role_mgmt/create_role_dialog.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_mgmt/group_list.dart';

import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_mgmt/dialog_type_enum.dart';
import 'package:provider/provider.dart';

class EnterpriseRolePage extends StatefulWidget {
  const EnterpriseRolePage({super.key});

  @override
  State<EnterpriseRolePage> createState() => _EnterpriseRoleState();
}

class _EnterpriseRoleState extends State<EnterpriseRolePage> {
  // Dio dio = Dio();
  // final String _token =
  //     "tCLQz+0RWr9GGU1b43xolDcdnBBZxLZFxfg9QszX6Wem9LbM34dsIe1qcLLuM1K4tjZAH86g8nvUdKkEj0Pt9d+3O3FTMatVTMKaPecZ7DXBpp/9PsfGGNHg5FqujNxLvZz/Dhrx1IBFVpo34uqBSCJnqG1091Vmc/XtLwUhsbEZvhovWl+sTzZIZy1cdjnLXHPSc4Q/QEm0DyTo3lxu1S/9C5BoZOV2vBWiwzpJtEs=";

  final GlobalKey<CreateGroupDialogState> _createGroupDialogStateKey =
      GlobalKey<CreateGroupDialogState>();

  final GlobalKey<CreateRoleDialogState> _createRoleDialogStateKey =
      GlobalKey<CreateRoleDialogState>();

  final GlobalKey<GroupListState> _groupListStateKey = GlobalKey<GroupListState>();

  var lineColor = Colors.red;
  double leftPanelWidth = 280;
  double titleHeight = 48;

  late AppTableStateManage _stateManage;

  String _keywords = '';

  late var colsTemp = [
    AppTableColumn(
      text: '姓名',
      field: 'Name',
      type: AppTableColumnType.text(),
      width: 100 + 200,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '工号',
      field: 'Number',
      type: AppTableColumnType.text(),
      width: 100 + 50,
      resizable: true, // 允许调整列宽
    ),
    AppTableColumn(
      text: '部门',
      field: 'manager',
      type: AppTableColumnType.text(),
      // alignment: Alignment.center,
      width: 100 + 50,
      resizable: true, // 允许调整列宽
      // headerBuilder: (context) {
      //   return Row(
      //     mainAxisAlignment: MainAxisAlignment.center,
      //     children: [
      //       Text('自定义列名称'),
      //       SizedBox(width: 4),
      //       Icon(Icons.star_border, size: 16, color: Colors.red),
      //     ],
      //   );
      // },
      cellBuilder: (context, value, column, row) {
        // 测试
        // String activeValueTemp = '行3列3';
        // return Container(
        //   padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        //   decoration: BoxDecoration(
        //     color: value == activeValueTemp ? Colors.green : Colors.red,
        //     borderRadius: BorderRadius.circular(4),
        //   ),
        //   child: Text(
        //     value == activeValueTemp ? '活跃' : '禁用',
        //     style: TextStyle(color: Colors.white),
        //   ),
        // );
        return Text('暂无数据，待对接');
      },
    ),

    AppTableColumn(
      text: '操作',
      field: 'Opt',
      type: AppTableColumnType.text(),
      // alignment: Alignment.center,
      width: 150,
      resizable: true, // 允许调整列宽
      showMore: false,
      // headerBuilder: (context) {
      //   return Row(
      //     mainAxisAlignment: MainAxisAlignment.center,
      //     children: [
      //       Text('自定义列名称'),
      //       SizedBox(width: 4),
      //       Icon(Icons.star_border, size: 16, color: Colors.red),
      //     ],
      //   );
      // },
      cellBuilder: (context, value, column, row) {
        return TextButton(
          onPressed: () {
            showDialog(
              context: context,
              builder:
                  (ctx) => AlertDialog(
                    title: Text('确认删除'),
                    content: Text('是否确认删除当前行？'),
                    actions: [
                      TextButton(onPressed: () => Navigator.pop(ctx), child: Text('取消')),
                      TextButton(
                        onPressed: () {
                          // 直接使用 _stateManage
                          Employee emp = Employee.fromJson(row);

                          deleteRow(context, emp.employeeId!);

                          Navigator.pop(ctx);
                        },
                        child: Text('删除'),
                      ),
                    ],
                  ),
            );
          },
          child: Text('删除'),
        );
      },
    ),

    // AppTableColumn(
    //   text: '人数',
    //   field: 'count',
    //   type: AppTableColumnType.number(
    //     defaultValue: 0,
    //     precision: 2,
    //     negative: false,
    //     format: '#,###', // 每三位数字使用逗号
    //   ),
    //   width: 100 + 100,
    //   resizable: true, // 允许调整列宽
    // ),
  ];

  // void _showDeleteDialog(BuildContext context, Map<String, dynamic> row) {
  //   showDialog(
  //     context: context,
  //     builder:
  //         (context) => AlertDialog(
  //           title: Text('确认删除'),
  //           content: Text('是否确认删除当前行？'),
  //           actions: [
  //             TextButton(onPressed: () => Navigator.pop(context), child: Text('取消')),
  //             TextButton(
  //               onPressed: () {
  //                 // 直接使用 _stateManage
  //                 Employee emp = Employee.fromJson(row);
  //                 print('删除员工: ${emp.name}----${emp.employeeId}');

  //                 // 使用 _stateManage 操作数据
  //                 // 注意：需要确保 _stateManage 已经初始化
  //                 if (_stateManage != null) {
  //                   // 执行删除操作
  //                   // _stateManage.removeRowById(emp.employeeId);
  //                 }

  //                 Navigator.pop(context);
  //               },
  //               child: Text('删除'),
  //             ),
  //           ],
  //         ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Column(
            children: [
              // _TitleBlock(title: '角色管理'),
              Expanded(
                child: Row(
                  children: [
                    Container(
                      width: leftPanelWidth,
                      decoration: BoxDecoration(
                        border: Border(right: BorderSide(color: context.border300, width: 1)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,

                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 13, horizontal: 16),
                            child: AppInput(
                              hintText: '请输入关键字',
                              size: InputSize.medium,
                              showErrMsg: false,
                              onChanged: (value) {
                                setState(() {
                                  _keywords = value;
                                });
                              },
                            ),

                            // AppBasicTextField(
                            //   initialValue: _keywords,
                            //   onChanged: (value) {
                            //     _keywords = value;
                            //     setState(() {});
                            //   },
                            //   decoration: InputDecoration(
                            //     border: OutlineInputBorder(
                            //       // borderSide: BorderSide(
                            //       //   color: Colors.grey,
                            //       //   width: 1,
                            //       // ),
                            //     ),
                            //     contentPadding: EdgeInsets.symmetric(horizontal: 10),
                            //     hintText: '请输入关键字',
                            //   ),
                            // ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              children: [
                                CreateGroupDialog(
                                  key: _createGroupDialogStateKey,
                                  onSuccess: () {
                                    _groupListStateKey.currentState?.getList();
                                  },
                                  child: AppButton(
                                    text: '创建分组',
                                    type: ButtonType.primary,
                                    onPressed: () {
                                      _createGroupDialogStateKey.currentState
                                          ?.showCreateGroupDialog(
                                            context,
                                            type: DialogTypeEmun.create,
                                          );
                                    },
                                  ),
                                ),
                                SizedBox(width: 10),

                                CreateRoleDialog(
                                  key: _createRoleDialogStateKey,
                                  onSuccess: () {
                                    _groupListStateKey.currentState?.getList();
                                  },
                                  // groups: _groupListStateKey.currentState?.groups,
                                  // roles: _groupListStateKey.currentState?.roles,
                                  child: AppButton(
                                    text: '创建角色',
                                    type: ButtonType.primary,
                                    onPressed: () {
                                      _createRoleDialogStateKey.currentState?.showCreateRoleDialog(
                                        context,
                                        type: DialogTypeEmun.create,
                                      );
                                    },
                                  ),
                                ),

                                Spacer(),
                                AppButton(
                                  iconData: Icons.expand,
                                  size: ButtonSize.small,
                                  type: ButtonType.default_,
                                  onPressed: () {
                                    _groupListStateKey.currentState?.toggleAll();

                                    // setState(() {
                                    //   groups.forEach((g) => g.isExpanded = !g.isExpanded);
                                    // });
                                  },
                                ),
                              ],
                            ),
                          ),

                          //分组列表
                          Expanded(
                            child: GroupList(
                              key: _groupListStateKey,
                              keywords: _keywords,
                              editGroup: (group) {
                                _createGroupDialogStateKey.currentState?.showCreateGroupDialog(
                                  context,
                                  type: DialogTypeEmun.edit,
                                  // id: group!.id,
                                  group: group,
                                );
                              },
                              editRole: (role) {
                                _createRoleDialogStateKey.currentState?.showCreateRoleDialog(
                                  context,
                                  type: DialogTypeEmun.edit,
                                  id: role!.id,
                                  // role: role,
                                );
                              },
                              onItemTap: (role) {
                                getUserList(role);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          _TitleBlock(
                            key: ValueKey('title_2'),
                            title: '负责人',
                            lineColor: context.textSecondary,
                            titleHeight: titleHeight,
                          ),

                          Padding(
                            padding: EdgeInsets.only(top: 10, right: 10, left: 10),
                            child: Row(
                              children: [
                                AppButton(
                                  text: '添加成员',
                                  iconData: IconFont.xianxing_tianjia,
                                  type: ButtonType.primary,
                                  onPressed: () {},
                                ),

                                SizedBox(width: 4),

                                AppButton(
                                  text: '模拟设置用户（丹顶鹤, 特靠谱, 穿山甲）',
                                  type: ButtonType.danger,
                                  onPressed: () {
                                    var empIds = [
                                      'c94a1904-87a8-4565-b7d2-0fe3d6261505',
                                      'aa5d7409-b87f-496e-b915-ad6ebad0c1b3',
                                      'e7fbdf8f-682b-4d1e-b7f9-def31d71517d',
                                    ];
                                    setUsersOfRole(empIds);
                                  },
                                ),
                              ],
                            ),
                          ),

                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.all(10),
                              child: AppTable(
                                // key: ValueKey(currentTabId),
                                // loading: isLoading,
                                // columns: columns,
                                // rows: rows,
                                // columns: [],
                                // rows: [],
                                checkType: TableCheckedEnum.multiple,
                                indexColumnWidth: 100,
                                defaultExpandAll: true,
                                maxLevel: 6,

                                uniqueId: 'EmployeeId',

                                /// 树形结构配置（扁平结构不需要传递树形结构配置参数）
                                // treeConfig: TreeTableConfig(
                                //   idField: 'nodeId',
                                //   parentIdField: 'pid',
                                //   emptyParentValue: null,
                                // ),
                                showAddRowButton: false,
                                showAddColumnButton: false,
                                // listviewPddingBottom: 0,

                                /// 索引列自定义
                                // indexCellBuilder: (context, index) {
                                //   return Text('第${index}条');
                                // },
                                onCellTap: (context, rowIndex, columnIndex, value) {
                                  String msg = '点击了第${rowIndex}行，第${columnIndex}列，值为：$value';
                                  print(msg);
                                },
                                onLoaded: (stateManage) {
                                  _stateManage = stateManage;
                                  // getList(true);

                                  _stateManage.setColumns(colsTemp);
                                },
                                onLoadMore: () {
                                  // loadMore();
                                },

                                // onAddColumn: (
                                //   context,
                                //   AppTableColumn column,
                                //   AppTableStateManage stateManager,
                                //   double tableViewportWidth,
                                // ) {
                                //   print('添加列：${column}');

                                //   if (columns.length < 10) {
                                //     setState(() {
                                //       //添加新列
                                //       columns.add(column);

                                //       //新增列的唯一标识符
                                //       var newField = column.field;

                                //       print(newField);

                                //       // //所有的行补充新增列属性
                                //       // for (var row in rows) {
                                //       //   row.data[newField] = '';
                                //       // }
                                //     });

                                //     // WidgetsBinding.instance.addPostFrameCallback((_) {
                                //     //   //直接使用传入的状态管理实例执行滚动调整逻辑
                                //     //   stateManager.adjustScrollPositionAfterAddColumn(
                                //     //     tableViewportWidth,
                                //     //     column.width,
                                //     //   );
                                //     // });
                                //   } else {
                                //     print('表格列不能超过10列-------------------');
                                //   }

                                //   // print('添加了新列：${column.field} ${column.text} ${column.width}');

                                //   // AppTableGeneralHelper.newColumn(1000);
                                // },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取用户列表数据
  Future<void> getUserList(Role role) async {
    var postDatas = {
      "PageSize": 10000,
      "PageIndex": 1,
      "RoleId": role.id, // 0198abab-f9b5-70a4-8c18-e6dd4f1a0175
    };
    try {
      _stateManage.setLoading(true);
      final response = await RoleAuthManagementApi.getUsersByRoleId(postDatas);

      if (mounted) {
        _stateManage.setLoading(false);
        var listJson = response['Items'];

        // List<Employee> list =
        //     (listJson as List).map((e) => Employee.fromJson(e as Map<String, dynamic>)).toList();

        List<Map<String, dynamic>> jsonRowsStr =
            (listJson as List).map((r) => r as Map<String, dynamic>).toList();
        var rows = _stateManage.convertToTreeData(jsonRowsStr);

        _stateManage.setRows(rows);
      }
    } catch (e) {
      _stateManage.setLoading(false);
    }
  }

  /// 设置用户
  Future<void> setUsersOfRole(List<String> empIds) async {
    try {
      var checkedRole = _groupListStateKey.currentState?.checkedRole;
      if (checkedRole != null) {
        _stateManage.setLoading(true);

        ///删除数据
        var postDatas = {'RoleId': checkedRole.id, 'EmployeeIdList': empIds};

        print('提交数据: $postDatas');

        await RoleAuthManagementApi.addOrEditRoleId(postDatas);

        /// 重新加载表格数据（刷新）
        getUserList(checkedRole);
      }
    } finally {
      _stateManage.setLoading(false);
    }
  }

  /// 删除
  Future<void> deleteRow(BuildContext context, String empId) async {
    try {
      var checkedRole = _groupListStateKey.currentState?.checkedRole;
      if (checkedRole != null) {
        _stateManage.setLoading(true);

        final tableState = context.read<AppTableStateManage>();

        ///删除数据
        var userIds = tableState.checkedRows.map((r) => r.id).toList();
        var postDatas = {'RoleId': checkedRole.id, 'EmployeeIdList': userIds};
        await RoleAuthManagementApi.addOrEditRoleId(postDatas);

        /// 表格删除行
        tableState.removeRow(empId);

        /// 重新加载表格数据（刷新）
        getUserList(checkedRole);
      }
    } finally {
      _stateManage.setLoading(false);
    }
  }
}

// 左右区块头标题组件
class _TitleBlock extends StatelessWidget {
  final String title;
  final double titleHeight;
  final Color lineColor;

  const _TitleBlock({
    super.key,
    required this.title,
    this.titleHeight = 48,
    this.lineColor = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: titleHeight,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: context.border300, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start, // 左对齐
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: context.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
