import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 角色树组件样式配置类
///
/// 统一管理角色树组件中的所有样式相关配置，包括：
/// - 布局尺寸和间距
/// - 文本样式
/// - 图标样式
/// - 颜色配置
/// - 动画配置
class RoleTreeStyles {
  // ==================== 布局尺寸配置 ====================

  /// 节点容器内边距
  static const EdgeInsets nodeContainerPadding = EdgeInsets.symmetric(
    vertical: 4.0,
    horizontal: 8.0,
  );

  /// 子节点左侧缩进距离
  static const EdgeInsets childNodeIndent = EdgeInsets.only(left: 20.0);

  /// 展开/收起图标区域宽度
  static const double expandIconAreaWidth = 20.0;

  /// 复选框与文本之间的间距
  static const double checkboxTextSpacing = 8.0;

  /// 角色图标与文本之间的间距
  static const double personIconTextSpacing = 8.0;

  // ==================== 图标样式配置 ====================

  /// 展开/收起图标
  static IconData get expandIcon => IconFont.mianxing_xiala;

  /// 展开/收起图标大小
  static const double expandIconSize = 20.0;

  /// 展开/收起图标颜色
  static Color expandIconColor(BuildContext context) {
    return context.icon100;
  }

  /// 角色图标
  static const IconData personIcon = Icons.manage_accounts_outlined;

  /// 角色图标大小
  static const double personIconSize = 16.0;

  /// 角色图标颜色
  static const Color personIconColor = Colors.blue;

  // ==================== 文本样式配置 ====================

  /// 节点名称默认文本样式
  static TextStyle nodeNameTextStyle(BuildContext context) {
    return TextStyle(fontSize: 14.0, color: context.textPrimary, fontWeight: FontWeight.normal);
  }

  /// 节点名称选中时文本样式
  static TextStyle nodeNameSelectedTextStyle(BuildContext context) {
    return TextStyle(fontSize: 14.0, color: context.textPrimary, fontWeight: FontWeight.w500);
  }

  /// 角色组节点文本样式
  static TextStyle groupNodeTextStyle(BuildContext context) {
    return TextStyle(fontSize: 14.0, color: context.textPrimary, fontWeight: FontWeight.w500);
  }

  /// 角色节点文本样式
  static TextStyle roleNodeTextStyle(BuildContext context) {
    return TextStyle(fontSize: 14.0, color: context.textPrimary, fontWeight: FontWeight.normal);
  }

  // ==================== 状态提示样式配置 ====================

  /// 加载提示文本样式
  static TextStyle loadingTextStyle(BuildContext context) {
    return TextStyle(fontSize: 14.0, color: context.textSecondary);
  }

  /// 错误提示文本样式
  static TextStyle errorTextStyle(BuildContext context) {
    return TextStyle(fontSize: 14.0, color: Colors.red);
  }

  /// 空数据提示文本样式
  static TextStyle emptyDataTextStyle(BuildContext context) {
    return TextStyle(fontSize: 14.0, color: context.textSecondary);
  }

  // ==================== 动画配置 ====================

  /// 展开/收起动画持续时间
  static const Duration expandAnimationDuration = Duration(milliseconds: 200);

  /// 展开状态下的旋转角度（0.0 表示向下，即原始状态）
  static const double expandedRotationTurns = 0.0;

  /// 收起状态下的旋转角度（-0.25 表示逆时针旋转90度，向右）
  static const double collapsedRotationTurns = -0.25;

  // ==================== 颜色配置 ====================

  /// 节点选中时的背景色
  static Color nodeSelectedBackgroundColor(BuildContext context) {
    return context.activeGrayColor.withValues(alpha: 0.1);
  }

  /// 节点悬停时的背景色
  static Color nodeHoverBackgroundColor(BuildContext context) {
    return context.activeGrayColor.withValues(alpha: 0.05);
  }

  // ==================== 复选框样式配置 ====================

  /// 复选框选中颜色
  static Color checkboxActiveColor(BuildContext context) {
    return AppColors.primary;
  }

  /// 复选框边框颜色
  static Color checkboxBorderColor(BuildContext context) {
    return context.border200;
  }

  // ==================== 工具方法 ====================

  /// 获取节点文本样式（根据节点类型和状态）
  static TextStyle getNodeTextStyle(
    BuildContext context, {
    required bool isGroup,
    required bool isSelected,
  }) {
    if (isSelected) {
      return nodeNameSelectedTextStyle(context);
    }

    return isGroup ? groupNodeTextStyle(context) : roleNodeTextStyle(context);
  }

  /// 获取展开图标的旋转角度
  static double getExpandIconRotation(bool isExpanded) {
    return isExpanded ? expandedRotationTurns : collapsedRotationTurns;
  }
}
