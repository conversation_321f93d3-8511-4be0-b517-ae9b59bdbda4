import 'package:json_annotation/json_annotation.dart';

part 'role_tree_model.g.dart';

/// 角色树形结构数据模型
/// 用于表示角色组和角色的层级关系
@JsonSerializable(explicitToJson: true, includeIfNull: false, fieldRename: FieldRename.pascal)
class RoleTreeModel {
  /// 唯一标识符
  @Json<PERSON>ey(name: 'Id')
  final String? id;

  /// 名称（角色组或角色姓名）
  @Json<PERSON>ey(name: 'Name', defaultValue: '')
  final String name;

  /// 父级ID
  @Json<PERSON>ey(name: 'ParentId')
  final String? parentId;

  /// 父级名称
  @Json<PERSON>ey(name: 'ParentName', defaultValue: '')
  final String parentName;

  /// 类型：1-角色分组，2-角色
  @JsonKey(name: 'Type', defaultValue: 1)
  final int type;

  /// 子节点列表（可选，用于树形结构）
  @<PERSON>son<PERSON><PERSON>(name: 'Children', defaultValue: <RoleTreeModel>[])
  final List<RoleTreeModel> children;

  /// 是否展开（UI状态，不参与序列化）
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool isExpanded;

  /// 是否选中（UI状态，不参与序列化）
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool isSelected;

  const RoleTreeModel({
    this.id,
    this.name = '',
    this.parentId,
    this.parentName = '',
    this.type = 1,
    this.children = const <RoleTreeModel>[],
    this.isExpanded = false,
    this.isSelected = false,
  });

  /// 从JSON创建实例
  factory RoleTreeModel.fromJson(Map<String, dynamic> json) {
    return _$RoleTreeModelFromJson(json);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$RoleTreeModelToJson(this);

  /// 从JSON列表创建实例列表
  static List<RoleTreeModel> fromJsonList(List<dynamic>? jsonList) {
    if (jsonList == null) return <RoleTreeModel>[];
    return jsonList.map((json) => RoleTreeModel.fromJson(json as Map<String, dynamic>)).toList();
  }

  /// 转换为JSON列表
  static List<Map<String, dynamic>> toJsonList(List<RoleTreeModel> models) {
    return models.map((model) => model.toJson()).toList();
  }

  /// 检查是否为分组
  bool get isGroup => type == 1;

  /// 检查是否为角色
  bool get isRole => type == 2;

  /// 是否有子节点
  bool get hasChildren => children.isNotEmpty;

  /// 获取完整路径名称（包含父级名称）
  String get fullName {
    if (parentName.isNotEmpty) {
      return '$parentName - $name';
    }
    return name;
  }

  /// 复制并修改属性
  RoleTreeModel copyWith({
    String? id,
    String? name,
    String? parentId,
    String? parentName,
    int? type,
    List<RoleTreeModel>? children,
    bool? isExpanded,
    bool? isSelected,
  }) {
    return RoleTreeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      parentName: parentName ?? this.parentName,
      type: type ?? this.type,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// 递归查找指定ID的节点
  RoleTreeModel? findById(String targetId) {
    if (id == targetId) return this;

    for (final child in children) {
      final found = child.findById(targetId);
      if (found != null) return found;
    }

    return null;
  }

  /// 获取所有叶子节点（角色）
  List<RoleTreeModel> get allRoles {
    final List<RoleTreeModel> roles = [];

    if (isRole) {
      roles.add(this);
    }

    for (final child in children) {
      roles.addAll(child.allRoles);
    }

    return roles;
  }

  /// 获取所有角色组节点
  List<RoleTreeModel> get allGroups {
    final List<RoleTreeModel> groups = [];

    if (isGroup) {
      groups.add(this);
    }

    for (final child in children) {
      groups.addAll(child.allGroups);
    }

    return groups;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RoleTreeModel && other.id == id && other.name == name && other.type == type;
  }

  @override
  int get hashCode => Object.hash(id, name, type);

  @override
  String toString() {
    return 'RoleTreeModel(id: $id, name: $name, type: $type, children: ${children.length})';
  }
}
