import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/views/admin/organization/member_department/create_department_dialog.dart';

/// 部门页
class DepartmentPage extends StatefulWidget {
  const DepartmentPage({super.key});

  @override
  State<DepartmentPage> createState() => _DepartmentPageState();
}

class _DepartmentPageState extends State<DepartmentPage> {
  // 创建 GlobalKey 用于访问 DepartmentSelector 的方法
  final GlobalKey<DepartmentTreeState> _departmentSelectorKey = GlobalKey<DepartmentTreeState>();
  final _departmentDialogStateKey = GlobalKey<DepartmentDialogState>();

  late AppTableStateManage _stateManage;

  /// 获取列表
  Future<void> getList() async {
    try {
      _stateManage.setLoading(true);
      final response = await DepartmentApi.getList({});

      List<dynamic> listJson = response['Items'];
      List<Map<String, dynamic>> jsonRowsStr =
          listJson.map((r) => r as Map<String, dynamic>).toList();

      var rows = _stateManage.convertToTreeData(jsonRowsStr);

      _stateManage.setRows(rows);
    } finally {
      _stateManage.setLoading(false);
    }
  }

  List<AppTableColumn> get colsTemp => [
    AppTableColumn(
      text: '部门名称',
      field: 'DepartmentName',
      type: AppTableColumnType.text(),
      width: 300,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '部门负责人',
      field: 'DepartmentHeadList',
      type: AppTableColumnType.text(),
      width: 200,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
      cellBuilder: (context, value, column, row) {
        if (value is List<dynamic>) {
          return Wrap(
            direction: Axis.horizontal,
            alignment: WrapAlignment.center,
            spacing: 5.0,
            runSpacing: 5.0,
            children:
                value.map<Widget>((t) {
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    spacing: 5,
                    children: [
                      ImageCacheUtil.cachedAvatarImage(imageUrl: t['Avatar']),
                      Text(t['Name']),
                    ],
                  );
                }).toList(),
          );
        }
        return SizedBox();
      },
    ),
    AppTableColumn(
      text: '操作',
      field: 'Opt',
      type: AppTableColumnType.text(),
      width: 80,
      resizable: false, // 允许调整列宽
      showMore: false,
      cellBuilder: (context, value, column, row) {
        return AppDropdown(
          items: [
            DropdownItem(text: '编辑', value: 'edit'),
            DropdownItem(text: '删除', value: 'delete'),
          ],
          trigger: DropdownTrigger.click,
          onItemSelected: (item) {
            switch (item.value) {
              case 'edit':
                _departmentDialogStateKey.currentState?.showCreateDepartmentDialog(
                  context,
                  type: DialogTypeEmun.edit,
                  id: row['Id'],
                );
                break;
              case 'delete':
                final id = row['Id']?.toString();
                deleteItem(id!);
                break;
              default:
            }
          },
          child: Icon(IconFont.xianxing_gengduo),
        );
      },
    ),
  ];

  /// 删除
  Future<void> deleteItem(String id) async {
    showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: const Text('您是否确定删除该项?'),
          actions: <Widget>[
            TextButton(child: const Text('取消'), onPressed: () => Navigator.of(context).pop(false)),
            TextButton(
              child: const Text('确定', style: TextStyle(color: AppColors.error)),
              onPressed: () async {
                final navigator = Navigator.of(context);
                try {
                  _stateManage.setLoading(true);
                  await DepartmentApi.delete([id]);
                  getList();
                } finally {
                  _stateManage.setLoading(false);
                }
                if (mounted) {
                  navigator.pop(true);
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 10,
        children: [
          //添加按钮
          CreateDepartmentDialog(
            key: _departmentDialogStateKey,
            onSuccess: () {
              _departmentSelectorKey.currentState?.refresh();
              getList();
            },
          ),
          Expanded(
            child: AppTable(
              checkType: TableCheckedEnum.none,
              indexColumnWidth: 100,
              defaultExpandAll: true,
              maxLevel: 6,
              treeConfig: TreeTableConfig(
                idField: 'Id',
                parentIdField: 'ParentId',
                emptyParentValue: null,
              ),
              onLoaded: (stateManage) {
                _stateManage = stateManage;
                getList();
                _stateManage.setColumns(colsTemp);
              },
            ),
          ),
        ],
      ),
    );
  }
}
