import 'package:octasync_client/imports.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/auth_group_model.dart';

class RoleAuthVar {
  static const List<SelectOption<int>> dataScopeenums = [
    SelectOption(label: '全组织', value: 1),
    SelectOption(label: '本部门及以下部门', value: 2),
    SelectOption(label: '本部门', value: 3),
    SelectOption(label: '本人数据', value: 4),
    SelectOption(label: '指定部门', value: 5),
  ];

  /// 动态生成分组数据（模拟数据）
  static List<AuthGroupModel> generateGroups() {
    return List.generate(10, (groupIndex) {
      final label = '分组-${groupIndex + 1}';
      final tip = '分组tip--${groupIndex + 1}';

      // 动态生成items
      final items = List.generate(15, (itemIndex) {
        final authId = (groupIndex + 1) * 100 + itemIndex + 1;
        final itemLabel = 'item ${authId}';
        final itemTip = itemIndex < 5 ? 'tip $authId' : '';

        return AuthItem(authId: authId, label: itemLabel, tip: itemTip);
      });

      return AuthGroupModel(label: label, tip: tip, items: items);
    });
  }
}
