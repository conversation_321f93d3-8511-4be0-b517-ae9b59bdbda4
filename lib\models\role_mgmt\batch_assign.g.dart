// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'batch_assign.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BatchAssign _$<PERSON>ch<PERSON>(Map<String, dynamic> json) => BatchAssign(
  roleIdList:
      (json['RoleIdList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  dataScopeenum: (json['DataScopeenum'] as num?)?.toInt() ?? 1,
  departmentIdList:
      (json['DepartmentIdList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
);

Map<String, dynamic> _$<PERSON>ch<PERSON>To<PERSON>son(BatchAssign instance) =>
    <String, dynamic>{
      'DataScopeenum': instance.dataScopeenum,
      'DepartmentIdList': instance.departmentIdList,
      'RoleIdList': instance.roleIdList,
    };
