import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:octasync_client/api/employee.dart';
import 'package:octasync_client/api/job.dart';
import 'package:octasync_client/components/form/phone_input/phone_input.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/components/selector/employee_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';
import 'package:octasync_client/models/role_mgmt/employee_department.dart';
import 'package:octasync_client/views/admin/organization/member_department/member_department_enum.dart';
import 'package:octasync_client/views/admin/organization/position_grade/model/position_model.dart';
import 'package:octasync_client/components/image_crop_dialog.dart';
import 'package:octasync_client/models/upload/upload_response.dart';
import 'package:file_picker/file_picker.dart';

/// 弹窗类型
enum DialogTypeEmun { create, edit }

// 人员创建
class CreateMemberDialog extends StatefulWidget {
  final void Function()? onSuccess; // 提交成功回调
  final Widget? child;

  const CreateMemberDialog({super.key, this.onSuccess, this.child});

  @override
  State<CreateMemberDialog> createState() => MemberDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class MemberDialogState extends State<CreateMemberDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  Employee _employeeData = Employee();

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  /// 选中的部门列表
  List<DepartmentModel> _checkedDepartments = [];

  /// 选中的直属上级员工列表
  List<Employee> _checkedSuperiorEmployeeList = [];

  /// 职位选项
  List<DropdownItem> _getPositionOptions = [];

  /// 部门对应职位映射
  /// key: 部门id ,value: 职位id
  Map<String, String?> _depPositionMap = {};

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final SelectController<int> _sexSelectController = SelectController<int>();
  final SelectController<String> _phoneAreaCodeController = SelectController<String>();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _numberController = TextEditingController();
  final TextEditingController _wxNumberController = TextEditingController();
  final TextEditingController _fixedPhoneController = TextEditingController();
  final SelectController<int> _employeeTypeController = SelectController<int>();

  /// 重置数据
  void resetFormData() {
    _employeeData = Employee();
    _nameController.text = '';
    _sexSelectController.clear();
    _phoneNumberController.text = '';
    _emailController.text = '';
    _checkedDepartments = [];
    _depPositionMap = {};
    _numberController.text = '';
    _wxNumberController.text = '';
    _fixedPhoneController.text = '';
    _employeeTypeController.clear();
    _checkedSuperiorEmployeeList = [];
  }

  @override
  void initState() {
    super.initState();
    getList();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _sexSelectController.dispose();
    _phoneAreaCodeController.dispose();
    _phoneNumberController.dispose();
    _emailController.dispose();
    _numberController.dispose();
    _fixedPhoneController.dispose();
    _employeeTypeController.dispose();
    super.dispose();
  }

  /// 获取职位列表
  Future<void> getList() async {
    final res = await JobApi.getListPage({});
    final pages = PagesModel<PositionModel>.fromJson(
      res,
      (json) => PositionModel.fromJson(json as Map<String, dynamic>),
    );
    _getPositionOptions =
        pages.items.map((e) => DropdownItem(value: e.id, text: e.name ?? '')).toList();
  }

  /// 根据职位id获取职位选项
  DropdownItem? getPositionOptionsById(String? id) {
    return _getPositionOptions.firstWhereOrNull((e) => e.value == id);
  }

  /// 添加/编辑
  Future<void> createRequest(BuildContext context, StateSetter setDialogState) async {
    if (!_formKey.currentState!.validate()) return;

    Employee newemployeeData = Employee.fromJson(_employeeData.toJson());
    newemployeeData.departmentList =
        _checkedDepartments.map((t) {
          return EmployeeDepartment(
            departmentId: t.id,
            departmentName: t.departmentName,
            jobTitleId: _depPositionMap[t.id],
          );
        }).toList();

    final params = newemployeeData.toJson();

    if (_dialogType == DialogTypeEmun.create) {
      params.remove('EmployeeId');
    }

    print('提交>>>>>>>>>>$params');

    try {
      setDialogState(() {
        btnLoading = true;
      });
      const apiMap = {
        DialogTypeEmun.create: EmployeeApi.add,
        DialogTypeEmun.edit: EmployeeApi.edit,
      };
      await apiMap[_dialogType]!(params);
      ToastManager.success('提交成功');
      widget.onSuccess?.call();
      resetFormData();
      // 创建模式且不继续添加，或编辑模式，则关闭弹窗
      if (mounted && (_dialogType == DialogTypeEmun.edit || !isAddNext)) {
        Navigator.of(context).pop();
      }
    } finally {
      setDialogState(() {
        btnLoading = false;
      });
    }
  }

  /// 通过详情接口获取并填充编辑数据
  Future<void> fillEditData(String id) async {
    try {
      // 调用详情接口获取完整数据
      final response = await EmployeeApi.getDetails({'Id': id});

      if (response != null) {
        // 将响应数据转换为 PositionModel
        _employeeData = Employee.fromJson(response);

        // 填充表单数据
        _nameController.text = _employeeData.name;
        _sexSelectController.setValue(_employeeData.sexEnum);
        _phoneAreaCodeController.setValue(_employeeData.phoneAreaCode);
        _phoneNumberController.text = _employeeData.phoneNumber;
        _emailController.text = _employeeData.email;
        _numberController.text = _employeeData.number;
        _wxNumberController.text = _employeeData.wxNumber;
        _fixedPhoneController.text = _employeeData.fixedPhoneNumber;
        _employeeTypeController.setValue(_employeeData.employeeTypeEnum);

        // 部门
        _checkedDepartments =
            _employeeData.departmentList!
                .map((e) => DepartmentModel(id: e.departmentId, departmentName: e.departmentName))
                .toList();
        // 职务
        for (var dep in _employeeData.departmentList!) {
          _depPositionMap[dep.departmentId!] = dep.jobTitleId;
        }

        // 直属上级
        if (_employeeData.superiorEmployeeId != null) {
          _checkedSuperiorEmployeeList = [
            Employee(
              employeeId: _employeeData.superiorEmployeeId,
              name: _employeeData.superiorEmployeeName ?? '',
            ),
          ];
        }
      }
    } catch (e) {
      ToastManager.error('获取详情失败');
    }
  }

  /// 打开添加部门弹窗
  void showDepartmentDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    String? id,
  }) async {
    // 重置表单数据
    resetFormData();

    // 如果是编辑模式，先加载数据再显示弹窗
    if (type == DialogTypeEmun.edit && id != null) {
      _employeeData.employeeId = id;
      await fillEditData(id);
    } else {
      _phoneAreaCodeController.setValue('+86');
      _employeeData.phoneAreaCode = '+86';
    }

    // 检查组件是否仍然挂载
    if (!mounted) return;

    _dialogType = type;

    double labelWidth = 85;

    /// 间距
    double spacing = 20;

    /// 名称
    Widget buildNameInput() {
      return AppInput(
        label: "姓名",
        labelWidth: labelWidth,
        required: true,
        labelPosition: LabelPosition.left,
        hintText: "姓名",
        size: InputSize.medium,
        controller: _nameController,
        maxLength: 30,
        validator: (value) {
          if (_employeeData.name.isEmpty) {
            return '请输入姓名';
          }
          return null;
        },
        onChanged: (value) {
          _employeeData.name = value;
        },
      );
    }

    /// 性别
    Widget buildSexSelect() {
      return AppFormField(
        label: '性别',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_sexSelectController.value == null) {
            return '请选择性别';
          }
          return null;
        },
        builder:
            (field) => AppSelect<int>(
              placeholder: '性别',
              options: MemberDepartmentEnum.sexOptions,
              controller: _sexSelectController,
              onChanged: (value) {
                field.didChange(value);
                _employeeData.sexEnum = value;
              },
            ),
      );
    }

    /// 手机号
    Widget buildPhoneInput() {
      return AppFormField(
        label: '手机号码',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_employeeData.phoneAreaCode.isEmpty && _employeeData.phoneNumber.isEmpty) {
            return '区号和手机号不能为空';
          } else if (_employeeData.phoneAreaCode.isEmpty) {
            return '区号不能为空';
          } else if (_employeeData.phoneNumber.isEmpty) {
            return '手机号不能为空';
          }
          return null;
        },
        builder:
            (field) => PhoneInput(
              areaCodeController: _phoneAreaCodeController,
              phoneController: _phoneNumberController,
              onChange: (areaCode, phoneNumber) {
                field.didChange(phoneNumber);
                _employeeData.phoneAreaCode = areaCode ?? '+86';
                _employeeData.phoneNumber = phoneNumber ?? '';
              },
            ),
      );
    }

    /// 邮箱
    Widget buildEmailInput() {
      return AppInput(
        label: "工作邮箱",
        labelWidth: labelWidth,
        required: true,
        labelPosition: LabelPosition.left,
        hintText: "工作邮箱",
        size: InputSize.medium,
        controller: _emailController,
        maxLength: 30,
        validator: (value) {
          final regex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
          if (!regex.hasMatch(value!) || value.isEmpty) {
            return '请输入正确的工作邮箱';
          }
          return null;
        },
        onChanged: (value) {
          _employeeData.email = value;
        },
      );
    }

    /// 部门选择
    Widget buildDepartmentSelect(StateSetter setDialogState) {
      return AppFormField(
        label: '部门',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_checkedDepartments.isEmpty) {
            return '请选择部门';
          }
          return null;
        },
        builder:
            (field) => DepartmentSelector(
              checkStrictly: true,
              defaultCheckedDepartment: _checkedDepartments,
              onChange: (value) {
                field.didChange(value);
                setDialogState(() {
                  _checkedDepartments = value;
                });
              },
            ),
      );
    }

    /// 职务(职位)
    Widget buildJobSelect() {
      return AppFormField(
        label: '职务',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          String? errMsg;
          if (_checkedDepartments.isEmpty) {
            errMsg = '请选择部门';
          } else {
            if (!_checkedDepartments.every((t) => _depPositionMap[t.id] != null)) {
              errMsg = '请选择职务';
            }
          }
          return errMsg;
        },
        builder:
            (field) => Container(
              width: double.infinity,
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                border: Border.all(color: context.border300),
                borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
              ),
              constraints: BoxConstraints(minHeight: 36),
              child: Column(
                spacing: 5,
                crossAxisAlignment: CrossAxisAlignment.start,
                children:
                    _checkedDepartments.map((t) {
                      return StatefulBuilder(
                        builder: (context, setPositionState) {
                          // 当前部门选中的职位
                          DropdownItem? curPositioned = getPositionOptionsById(
                            _depPositionMap[t.id],
                          );
                          return IntrinsicWidth(
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: context.background200,
                                borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(t.departmentName, style: TextStyle(fontSize: 12)),
                                  const SizedBox(width: 8),
                                  Container(width: 1, height: 16, color: context.border300),
                                  const SizedBox(width: 8),
                                  AppDropdown(
                                    text: curPositioned?.text ?? '请选择职务',
                                    items: _getPositionOptions,
                                    size: DropdownSize.small,
                                    onItemSelected: (item) {
                                      field.didChange(item);
                                      setPositionState(() {
                                        _depPositionMap[t.id!] = item.value;
                                      });
                                    },
                                    value: _depPositionMap[t.id],
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
                    }).toList(),
              ),
            ),
      );
    }

    /// 工号
    Widget buildNumberInput() {
      return AppInput(
        label: "工号",
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "工号",
        size: InputSize.medium,
        controller: _numberController,
        inputFormatters: [FilteringTextInputFormatter(RegExp(r'[0-9]'), allow: true)],
        maxLength: 30,
        validator: (value) {
          return null;
        },
        onChanged: (value) {
          _employeeData.number = value;
        },
      );
    }

    /// 微信
    Widget buildWxNumberInput() {
      return AppInput(
        label: "微信号",
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "微信号",
        size: InputSize.medium,
        controller: _wxNumberController,
        maxLength: 20,
        validator: (value) {
          return null;
        },
        onChanged: (value) {
          _employeeData.wxNumber = value;
        },
      );
    }

    /// 固定电话
    Widget buildFixedPhoneInput() {
      return AppInput(
        label: "固定电话",
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "固定电话",
        size: InputSize.medium,
        controller: _fixedPhoneController,
        maxLength: 20,
        validator: (value) {
          return null;
        },
        onChanged: (value) {
          _employeeData.fixedPhoneNumber = value;
        },
      );
    }

    /// 头像
    Widget buildAvatar(context) {
      double width = 80;
      double height = 80;

      /// 标识悬停状态变量
      bool isSignHovering = false;

      Future<void> editAvatar(context, setState) async {
        PlatformFile? file = await FileUtil.pickSingleFile(type: FileType.image);
        if (file != null && file.bytes != null) {
          if (!mounted) return;

          UploadResponse? res = await ImageCropDialog.show(context: context, imageFile: file);

          if (res == null) return;

          setState(() {
            _employeeData.avatarId = res.fileId;
            _employeeData.avatar = res.path!;
          });
        }
      }

      return AppFormField(
        label: '头像',
        required: false,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          return null;
        },
        builder:
            (field) => StatefulBuilder(
              builder:
                  (context, setState) => MouseRegion(
                    cursor: SystemMouseCursors.click,
                    onEnter: (_) => setState(() => isSignHovering = true),
                    onExit: (_) => setState(() => isSignHovering = false),
                    child: GestureDetector(
                      onTap: () => editAvatar(context, setState),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          ImageCacheUtil.cachedNetworkImage(
                            imageUrl: _employeeData.avatar,
                            width: width,
                            height: height,
                          ),
                          if (isSignHovering)
                            Container(
                              width: width,
                              height: height,
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                              ),
                              child: Center(
                                child: Text(
                                  '修改图片',
                                  style: TextStyle(color: Colors.white, fontSize: 12),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
            ),
      );
    }

    /// 人员类型
    Widget buildMemberType() {
      return AppFormField(
        label: '人员类型',
        required: false,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          return null;
        },
        builder:
            (field) => AppSelect<int>(
              placeholder: '人员类型',
              options: MemberDepartmentEnum.employeeTypeOptions,
              controller: _employeeTypeController,
              onChanged: (value) {
                field.didChange(value);
                _employeeData.employeeTypeEnum = value!;
              },
            ),
      );
    }

    /// 直属上级
    Widget buildSuperiorEmployee() {
      return AppFormField(
        label: '直属上级',
        required: false,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          return null;
        },
        builder:
            (field) => EmployeeSelector(
              defaultCheckedEmployeeList: _checkedSuperiorEmployeeList,
              maxSelectableEmployees: 1,
              onChange: (value) {
                field.didChange(value);
                _checkedSuperiorEmployeeList = value;
                _employeeData.superiorEmployeeId = value.firstOrNull?.employeeId;
              },
            ),
      );
    }

    AppDialog.show(
      width: 480,
      context: context,
      title: _dialogType == DialogTypeEmun.create ? '添加成员' : '编辑成员',
      isDrawer: true,
      barrierDismissible: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      child: StatefulBuilder(
        builder: (BuildContext context, StateSetter setDialogState) {
          return Column(
            children: [
              Expanded(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildNameInput(),
                      buildSexSelect(),
                      buildPhoneInput(),
                      buildEmailInput(),
                      buildDepartmentSelect(setDialogState),
                      buildJobSelect(),
                      buildNumberInput(),
                      SizedBox(height: spacing),
                      buildWxNumberInput(),
                      SizedBox(height: spacing),
                      buildFixedPhoneInput(),
                      SizedBox(height: spacing),
                      buildAvatar(context),
                      Row(
                        children: [
                          Text(
                            '个人资料页不展示信息',
                            style: TextStyle(fontSize: 12, color: AppColors.textHint),
                          ),
                          Expanded(child: Divider(height: 1, color: context.border200)),
                        ],
                      ),
                      SizedBox(height: spacing),
                      buildMemberType(),
                      buildSuperiorEmployee(),
                      //TODO:需要角色选择组件完成
                    ],
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 只有创建模式才显示"继续新建下一条"选项
                    if (_dialogType == DialogTypeEmun.create) ...[
                      Checkbox(
                        value: isAddNext,
                        onChanged: (value) {
                          setDialogState(() {
                            isAddNext = !isAddNext;
                          });
                        },
                      ),
                      Text('继续新建下一条'),
                      const SizedBox(width: 10),
                    ],
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      loading: btnLoading,
                      onPressed: () => createRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
