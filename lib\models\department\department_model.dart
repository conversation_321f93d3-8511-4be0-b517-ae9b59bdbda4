import 'package:json_annotation/json_annotation.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';
part 'department_model.g.dart';

@JsonSerializable()
class DepartmentModel {
  @Json<PERSON>ey(name: 'ParentIdList', defaultValue: [])
  List<String> parentIdList;
  @Json<PERSON>ey(name: 'ParentList', defaultValue: [])
  List<DepartmentModel> parentList;

  @Json<PERSON>ey(name: 'Id')
  String? id;
  @Json<PERSON>ey(name: 'DepartmentName', defaultValue: '')
  String departmentName;
  @JsonKey(name: 'ParentId')
  String? parentId;
  @Json<PERSON><PERSON>(name: 'ParentName')
  String? parentName;

  /// 次要上级部门
  @Json<PERSON><PERSON>(name: 'SecondarySuperiorIds')
  String? secondarySuperiorIds;

  /// 部门负责人
  @<PERSON>son<PERSON>ey(name: 'DepartmentHeadIdList', defaultValue: [])
  List<String> departmentHeadIdList;
  @Json<PERSON>ey(name: 'DepartmentHeadList', defaultValue: [])
  List<Employee> departmentHeadList;

  /// 部门HRBP
  @Json<PERSON><PERSON>(name: 'DepartmentHRBPId')
  String? departmentHrbpId;
  @JsonKey(name: 'DepartmentHRBPName')
  String? departmentHrbpName;

  /// 描述
  @JsonKey(name: 'Description', defaultValue: '')
  String description;

  DepartmentModel({
    this.parentIdList = const [],
    this.parentList = const [],
    this.departmentHeadIdList = const [],
    this.departmentHeadList = const [],
    this.id,
    this.departmentName = '',
    this.parentId,
    this.parentName = '',
    this.secondarySuperiorIds,
    this.departmentHrbpId,
    this.departmentHrbpName,
    this.description = '',
  });

  factory DepartmentModel.fromJson(Map<String, dynamic> json) {
    return _$DepartmentModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DepartmentModelToJson(this);
}
