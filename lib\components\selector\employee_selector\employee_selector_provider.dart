import 'package:flutter/material.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree_model.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree.dart';

/// 员工选择器状态管理 Provider
class EmployeeSelectorProvider extends ChangeNotifier {
  /// 当前选中的员工列表
  List<EmployeeTreeModel> _checkedEmployees = [];

  /// 搜索关键词
  String _searchQuery = '';

  /// 默认选中的员工ID列表
  List<String> _defaultCheckedEmployeeIds = [];

  /// 最大可选择员工数量，为null时表示无限制
  int? _maxSelectableEmployees;

  /// 禁用选择的员工ID列表
  List<String> _disabledIdList = [];

  /// 获取当前选中的员工列表
  List<EmployeeTreeModel> get checkedEmployees => List.unmodifiable(_checkedEmployees);

  /// 获取当前搜索关键词
  String get searchQuery => _searchQuery;

  /// 获取选中员工数量
  int get checkedCount => _checkedEmployees.length;

  /// 获取默认选中的员工ID列表
  List<String> get defaultCheckedEmployeeIds => List.unmodifiable(_defaultCheckedEmployeeIds);

  /// 获取最大可选择员工数量
  int? get maxSelectableEmployees => _maxSelectableEmployees;

  /// 获取禁用选择的员工ID列表
  List<String> get disabledIdList => List.unmodifiable(_disabledIdList);

  /// 检查是否可以添加更多员工
  bool get canAddMoreEmployees {
    if (_maxSelectableEmployees == null) return true;
    return _checkedEmployees.length < _maxSelectableEmployees!;
  }

  /// 检查是否已达到最大选择限制
  bool get isMaxSelectionReached {
    if (_maxSelectableEmployees == null) return false;
    return _checkedEmployees.length >= _maxSelectableEmployees!;
  }

  /// 设置默认选中的员工ID列表
  void setDefaultCheckedEmployeeIds(List<String> employeeIds) {
    _defaultCheckedEmployeeIds = List.from(employeeIds);
    notifyListeners();
  }

  /// 设置最大可选择员工数量
  void setMaxSelectableEmployees(int? maxCount) {
    _maxSelectableEmployees = maxCount;
    // 如果当前选中数量超过新的限制，需要截断
    if (maxCount != null && _checkedEmployees.length > maxCount) {
      _checkedEmployees = _checkedEmployees.take(maxCount).toList();
    }
    notifyListeners();
  }

  /// 设置禁用选择的员工ID列表
  void setDisabledIdList(List<String> disabledIds) {
    _disabledIdList = List.from(disabledIds);
    notifyListeners();
  }

  /// 检查员工ID是否在默认选中列表中
  bool isDefaultChecked(String employeeId) {
    return _defaultCheckedEmployeeIds.contains(employeeId);
  }

  /// 检查员工ID是否被禁用选择
  bool isDisabled(String employeeId) {
    return _disabledIdList.contains(employeeId);
  }

  /// 更新选中的员工列表
  void updateCheckedEmployees(List<EmployeeTreeModel> employees) {
    _checkedEmployees = List.from(employees);
    notifyListeners();
  }

  /// 添加选中的员工
  void addCheckedEmployee(EmployeeTreeModel employee) {
    if (!_checkedEmployees.any((e) => e.id == employee.id)) {
      // 检查是否超过最大选择限制
      if (_maxSelectableEmployees != null && _checkedEmployees.length >= _maxSelectableEmployees!) {
        return; // 不添加，已达到最大限制
      }
      _checkedEmployees.add(employee);
      notifyListeners();
    }
  }

  /// 移除选中的员工
  void removeCheckedEmployee(String employeeId) {
    // 如果员工被禁用，不允许移除
    if (isDisabled(employeeId)) {
      return;
    }
    _checkedEmployees.removeWhere((e) => e.id == employeeId);
    notifyListeners();
  }

  /// 清空所有选中的员工
  void clearAllCheckedEmployees() {
    // 只移除非禁用的员工，保留禁用的员工
    _checkedEmployees.removeWhere((employee) => !isDisabled(employee.id!));
    notifyListeners();
  }

  /// 设置搜索关键词
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// 重置状态
  void reset() {
    _checkedEmployees.clear();
    _searchQuery = '';
    notifyListeners();
  }

  /// 重置到默认选中状态（保留默认选中的员工）
  void resetToDefault() {
    _checkedEmployees.clear();
    _searchQuery = '';
    // 注意：这里不清空 _defaultCheckedEmployeeIds，因为它们应该保持
    notifyListeners();
  }

  /// 应用默认选中状态到员工树（数据加载完成后调用）
  void applyDefaultCheckedState(GlobalKey<EmployeeTreeState> treeKey) {
    if (_defaultCheckedEmployeeIds.isNotEmpty) {
      final treeState = treeKey.currentState;
      if (treeState != null) {
        // 先重置所有选中状态
        treeState.resetAllNodesCheck();
        // 然后设置默认选中的员工
        for (final employeeId in _defaultCheckedEmployeeIds) {
          // 如果员工被禁用，使用强制选中方法
          if (isDisabled(employeeId)) {
            treeState.forceCheckNode(employeeId);
          } else {
            treeState.checkNode(employeeId);
          }
        }
        // 更新Provider中的选中列表
        final checkedEmployees = treeState.getAllCheckedEmployees();
        updateCheckedEmployees(checkedEmployees);
      }
    }
  }
}
