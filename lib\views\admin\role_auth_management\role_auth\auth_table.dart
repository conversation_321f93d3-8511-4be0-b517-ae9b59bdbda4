import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/auth_group_model.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/role_auth_var.dart';

class AuthTable extends StatefulWidget {
  const AuthTable({super.key});

  @override
  State<AuthTable> createState() => AuthTableState();
}

class AuthTableState extends State<AuthTable> {
  /// 权限组
  late List<AuthGroupModel> groups;

  /// 所有权限组包含的权限
  List<AuthItem> get authList => groups.expand((g) => g.items).cast<AuthItem>().toList();

  /// 所有选中的权限编号
  List<int> get checkedAuthIds => authList.where((a) => a.isChecked).map((a) => a.authId).toList();

  @override
  void initState() {
    super.initState();
    groups = RoleAuthVar.generateGroups();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // decoration: BoxDecoration(
      //   border: Border.all(color: Colors.red),
      //   borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      // ),
      child: Builder(
        builder:
            (innerContext) => LayoutBuilder(
              builder: (ctx, constraints) {
                var panelWidth = constraints.maxWidth;

                return ListView.builder(
                  itemCount: groups.length,
                  cacheExtent: 5,
                  itemBuilder: (context, index) {
                    return _buildGroup(groups[index], panelWidth, index);
                  },
                );
              },
            ),
      ),
    );
  }

  /// [group] 分组参数
  /// [panelWidth] 显示的面板宽度
  Widget _buildGroup(AuthGroupModel group, double panelWidth, int idxOfGroup) {
    // 显示面板的水平边距
    double panelHorizontalPadding = 17;
    // 显示面板的上下边距
    double panelVerticalPadding = 8;
    // 一行显示三个
    int colCount = 3;
    // 每一项所占宽度
    double itemWidth = (panelWidth - panelHorizontalPadding * (colCount + 1)) / colCount;

    var borderStyle = BorderSide(color: context.border300, width: 1.0);

    return Container(
      // margin: EdgeInsets.symmetric(vertical: 5),
      child: Column(
        children: [
          Container(
            height: 36,
            padding: EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              color: context.background200,
              // border: Border.all(color: Colors.red),
              border: Border(
                top: borderStyle,
                left: borderStyle,
                right: borderStyle,
                bottom:
                    idxOfGroup == groups.length - 1 && !group.isExpanded
                        ? borderStyle
                        : BorderSide.none,
              ),
              borderRadius:
                  idxOfGroup == 0
                      ? BorderRadius.only(
                        topLeft: Radius.circular(AppRadiusSize.radius4),
                        topRight: Radius.circular(AppRadiusSize.radius4),
                      )
                      : idxOfGroup == groups.length - 1 && !group.isExpanded
                      ? BorderRadius.only(
                        bottomLeft: Radius.circular(AppRadiusSize.radius4),
                        bottomRight: Radius.circular(AppRadiusSize.radius4),
                      )
                      : null,
            ),

            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    _handleToggle(group);
                  },
                  child: AnimatedRotation(
                    key: UniqueKey(),
                    turns: group.isExpanded ? 0 : -0.25,
                    duration: Duration(milliseconds: 300),
                    child: Icon(IconFont.mianxing_xiala, color: context.icon200, size: 16),
                  ),
                ),
                Checkbox(
                  value: group.isChecked,
                  tristate: true,
                  onChanged: (value) {
                    bool isChecked = group.isChecked == true ? false : true;
                    _handleCheckedGroup(group, isChecked);
                  },
                ),
                Text(group.label, style: TextStyle(fontWeight: FontWeight.w600)),
                _buildTipIcon(group.tip),
              ],
            ),
          ),

          if (group.isExpanded == true)
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                vertical: panelVerticalPadding,
                horizontal: panelVerticalPadding,
              ),
              decoration: BoxDecoration(
                border: Border(
                  top: borderStyle,
                  left: borderStyle,
                  right: borderStyle,
                  bottom: idxOfGroup == groups.length - 1 ? borderStyle : BorderSide.none,
                ),
                borderRadius:
                    idxOfGroup == groups.length - 1
                        ? BorderRadius.only(
                          bottomLeft: Radius.circular(AppRadiusSize.radius4),
                          bottomRight: Radius.circular(AppRadiusSize.radius4),
                        )
                        : null,
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 0, horizontal: panelHorizontalPadding),
                child: Wrap(
                  spacing: 8.0, // 主轴间距
                  runSpacing: 8.0, // 交叉轴间距
                  children:
                      group.items.map((item) {
                        return SizedBox(
                          width: itemWidth,
                          child: Container(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Checkbox(
                                  value: item.isChecked,
                                  onChanged: (value) {
                                    _handleCheckedAuthItem(item, value!, group);
                                  },
                                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                ),
                                Text(item.label, overflow: TextOverflow.ellipsis),
                                _buildTipIcon(item.tip),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建提示小问号组件
  Widget _buildTipIcon(String? tip) {
    if (tip != null && tip != '') {
      return AppTooltip(
        message: tip,
        child: Padding(
          padding: EdgeInsets.only(left: 4),
          child: Icon(IconFont.mianxing_shuoming, color: context.icon200, size: 16),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  /// 设置分组想是否选中
  void _handleCheckedAuthItem(AuthItem item, bool isChecked, AuthGroupModel group) {
    setState(() {
      item.isChecked = isChecked;
      _updateGroupChecked(group);
    });
  }

  /// 被动设置分组选中状态
  void _updateGroupChecked(AuthGroupModel group) {
    bool? isAllChecked = false;
    var items = group.items;
    if (items.isNotEmpty) {
      if (items.every((i) => i.isChecked)) {
        isAllChecked = true;
      } else if (items.every((i) => !i.isChecked)) {
        isAllChecked = false;
      } else {
        isAllChecked = null;
      }
    }
    group.isChecked = isAllChecked;
  }

  // 组折叠展开
  void _handleToggle(AuthGroupModel group) {
    setState(() {
      group.isExpanded = !group.isExpanded;
    });
  }

  // 点击选中组（分组权限）
  void _handleCheckedGroup(AuthGroupModel group, bool isChecked) {
    setState(() {
      group.isChecked = isChecked;
      for (var item in group.items) {
        item.isChecked = isChecked;
      }
    });
  }

  /// 通过权限ID列表设置选中的项目
  void setCheckedAuthItems(List<int> authIds) {
    setState(() {
      // 遍历所有分组和项目
      for (var group in groups) {
        for (var item in group.items) {
          // 如果权限ID在传入的列表中，则设置为选中，否则设置为未选中
          item.isChecked = authIds.contains(item.authId);
        }
        // 更新分组的选中状态
        _updateGroupChecked(group);
      }
    });
  }
}
