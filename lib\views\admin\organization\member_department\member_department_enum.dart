import 'package:octasync_client/imports.dart';

/// 部门与成员枚举
class MemberDepartmentEnum {
  /// 私有构造函数，防止实例化
  MemberDepartmentEnum._();

  /// 性别
  static const List<SelectOption<int>> sexOptions = [
    SelectOption(value: 1, label: '男'),
    SelectOption(value: 2, label: '女'),
    SelectOption(value: 3, label: '保密'),
  ];

  /// 人员类型
  static const List<SelectOption<int>> employeeTypeOptions = [
    SelectOption(value: 1, label: '正式'),
    SelectOption(value: 2, label: '实习'),
    SelectOption(value: 3, label: '外包'),
    SelectOption(value: 4, label: '劳务'),
    SelectOption(value: 5, label: '顾问'),
  ];
}
