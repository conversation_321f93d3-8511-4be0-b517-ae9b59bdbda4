import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/components/selector/role_selector/role_tree.dart';
import 'package:octasync_client/components/selector/role_selector/role_selector_provider.dart';
import 'package:octasync_client/imports.dart';

/// 角色选择对话框内容
class RoleSelectorDialog extends StatelessWidget {
  final GlobalKey<RoleTreeState> roleSelectorKey;

  const RoleSelectorDialog({super.key, required this.roleSelectorKey});

  /// 更新选中的角色列表
  void _updateCheckedRoles(BuildContext context) {
    final checkedRoles = roleSelectorKey.currentState?.getAllCheckedRoles() ?? [];
    context.read<RoleSelectorProvider>().updateCheckedRoles(checkedRoles);
  }

  @override
  Widget build(BuildContext context) {
    // 重置状态到默认选中
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<RoleSelectorProvider>().resetToDefault();
    });

    return Row(
      children: [
        // 左侧角色树状
        Expanded(
          child: Container(
            width: 250,
            decoration: BoxDecoration(border: Border(right: BorderSide(color: context.border300))),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: context.border300)),
                  ),
                  child: AppInput(
                    hintText: '搜索',
                    size: InputSize.medium,
                    showErrMsg: false,
                    onChanged: (value) {
                      roleSelectorKey.currentState?.setSearchQuery(value);
                      context.read<RoleSelectorProvider>().setSearchQuery(value);
                    },
                  ),
                ),
                Expanded(
                  child: RoleTree(
                    key: roleSelectorKey,
                    showCheckbox: true,
                    onNodeSelected: (role, value) {
                      _updateCheckedRoles(context);
                    },
                    onDataLoaded: () {
                      // 数据加载完成后展开顶级角色组并应用默认选中状态
                      roleSelectorKey.currentState?.expandTopLevelGroups();
                      context.read<RoleSelectorProvider>().applyDefaultCheckedState(
                        roleSelectorKey,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        // 右侧已选列表
        Expanded(
          child: Consumer<RoleSelectorProvider>(
            builder: (context, provider, child) {
              final checkedRoles = provider.checkedRoles;
              return Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('已选择(${provider.checkedCount})'),
                        AppButton(
                          text: '清空全部',
                          type: ButtonType.primary,
                          textOnly: true,
                          onPressed: () {
                            roleSelectorKey.currentState?.resetAllNodesCheck();
                            provider.clearAllCheckedRoles();
                          },
                        ),
                      ],
                    ),
                    Divider(color: context.border300),
                    Expanded(
                      child: ListView.builder(
                        itemCount: checkedRoles.length,
                        itemBuilder: (context, index) {
                          final role = checkedRoles[index];
                          return Padding(
                            padding: const EdgeInsets.only(top: 5, bottom: 5, right: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(role.name, overflow: TextOverflow.ellipsis),
                                      if (role.parentName.isNotEmpty)
                                        Text(
                                          role.parentName,
                                          style: TextStyle(color: AppColors.textHint, fontSize: 12),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                    ],
                                  ),
                                ),
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () {
                                      roleSelectorKey.currentState?.uncheckNode(role.id!);
                                      provider.removeCheckedRole(role.id!);
                                    },
                                    child: Icon(
                                      IconFont.mianxing_jianshao,
                                      color: AppColors.error,
                                      size: AppIconSize.medium,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
