import 'dart:async';
import 'dart:convert';
// import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:dio/dio.dart';
import 'package:jyt_components_package/jyt_components_package.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';

class TableDemo extends StatefulWidget {
  const TableDemo({super.key});

  @override
  State<TableDemo> createState() => _TableDemoState();
}

class _TableDemoState extends State<TableDemo> {
  Dio dio = Dio();

  late AppTableStateManage _stateManage;

  late AppCustomTooltipController _addColumnTooltipController;

  @override
  void initState() {
    super.initState();

    _addColumnTooltipController = AppCustomTooltipController();
  }

  @override
  void dispose() {
    // 移除监听
    _addColumnTooltipController.dispose();
    super.dispose();
  }

  var colsTemp = [
    AppTableColumn(
      text: '部门名称',
      field: 'name',
      type: AppTableColumnType.text(),
      width: 100 + 200,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '性别',
      field: 'sex',
      type: AppTableColumnType.text(),
      width: 100 + 50,
      resizable: true, // 允许调整列宽
    ),
    AppTableColumn(
      text: '负责人',
      field: 'manager',
      type: AppTableColumnType.text(),
      // alignment: Alignment.center,
      width: 100 + 50,
      resizable: true, // 允许调整列宽
      headerBuilder: (context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('自定义列名称'),
            SizedBox(width: 4),
            Icon(Icons.star_border, size: 16, color: Colors.red),
          ],
        );
      },
      cellBuilder: (context, value, column, row) {
        // 测试
        String activeValueTemp = '行3列3';
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: value == activeValueTemp ? Colors.green : Colors.red,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            value == activeValueTemp ? '活跃' : '禁用',
            style: TextStyle(color: Colors.white),
          ),
        );
      },
    ),
    AppTableColumn(
      text: '人数',
      field: 'count',
      type: AppTableColumnType.number(
        defaultValue: 0,
        precision: 2,
        negative: false,
        format: '#,###', // 每三位数字使用逗号
      ),
      width: 100 + 100,
      resizable: true, // 允许调整列宽
    ),
    // AppTableColumn(
    //   Text: 'BB',
    //   field: 'col_1',
    //   width: 100 + 150,
    //   resizable: true, // 允许调整列宽
    // ),
    // AppTableColumn(
    //   Text: 'BB',
    //   field: 'col_2',
    //   width: 100 + 150,
    //   resizable: true, // 允许调整列宽
    // ),
    // AppTableColumn(
    //   Text: 'BB',
    //   field: 'col_3',
    //   width: 100 + 150,
    //   resizable: true, // 允许调整列宽
    // ),
    // AppTableColumn(
    //   Text: 'BB',
    //   field: 'col_4',
    //   width: 100 + 150,
    //   resizable: true, // 允许调整列宽
    // ),
  ];

  var rowsTemp = [
    {'nodeId': '9', 'name': '999', 'pid': null, 'sex': '1', 'count': '50000'},
    {'nodeId': '10', 'name': 'AAAA', 'pid': null, 'sex': '1', 'count': '5000000.00'},

    // {'nodeId': '12', 'name': 'CCCC', 'pid': null},
    // {'nodeId': '13', 'name': 'DDDD', 'pid': null},
    {'nodeId': '7', 'name': '777', 'pid': null, 'sex': '1', 'count': '5000000.20'},
    {'nodeId': '8', 'name': '888', 'pid': '7', 'sex': '1', 'count': '5000000'},
    {'nodeId': '11', 'name': 'BBBB', 'pid': null, 'sex': '1', 'count': '5000000'},

    // {'nodeId': '1', 'name': '技术部', 'pid': null, 'sex': '1', 'count': '5000000'},
    // {'nodeId': '2', 'name': '前端组', 'pid': '1', 'sex': '1', 'count': '5000000'},
    // {'nodeId': '3', 'name': '后端组', 'pid': '1', 'sex': '1', 'count': '5000000'},
    // {'nodeId': '4', 'name': 'Web组', 'pid': '2', 'sex': '1', 'count': '5000000'},
    // {
    //   'nodeId': '41',
    //   'name': 'Web组-1',
    //   'pid': '4',
    //   'sex': '1',
    //   'count': '5000000',
    // },
    // {
    //   'nodeId': '411',
    //   'name': 'Web组-1-1',
    //   'pid': '41',
    //   'sex': '1',
    //   'count': '50',
    // },
    // // {'nodeId': '4111', 'name': 'Web组-1-1-1', 'pid': '411'},
    // // {'nodeId': '42', 'name': 'Web组-2', 'pid': '4'},
    // {'nodeId': '5', 'name': '移动组', 'pid': '2', 'sex': '1', 'count': '50'},
    // {'nodeId': '55', 'name': '移动组-1', 'pid': '5', 'sex': '1', 'count': '50'},
    // {'nodeId': '6', 'name': 'Java组', 'pid': '3', 'sex': '1', 'count': '50'},
  ];

  String oldColsJson = '';
  String oldRowsJson = '';

  // bool isLoading = false;
  List<AppTableColumn> columns = [];
  List<Map<String, dynamic>> rows = [];

  List<String> tables = [];
  //当前表格主键
  String currentTabId = '';

  Future<bool> fetchData() {
    return Future.delayed(Duration(seconds: 5), () {
      return true; // 模拟数据加载
    }); // 模拟延迟（例如：等待网络请求）
  }

  void clearData(bool isAll) {
    if (isAll) {
      _stateManage.clear();
    } else {
      _stateManage.clearRows();
    }

    _stateManage.setHasMore(true);
  }

  void getList(bool isAll) {
    // setState(() {
    //   isLoading = true;
    // });

    _stateManage.setLoading(true);

    fetchData()
        .then((result) {
          print('接口返回数据：${result}');
          _stateManage.setLoading(false);

          // setState(() {
          // _stateManage.setLoading(false);
          // isLoading = false;

          var colList = colsTemp.map((c) => c.clone()).toList();

          if (isAll) {
            // columns = colsTemp;

            _stateManage.setColumns(colList);
          }

          // 行数据示例
          rows = rowsTemp;

          _stateManage.setRows(_stateManage.convertToTreeData(rows));
          // });
        })
        .catchError((err) {
          print(err);
        });
  }

  void _handleAopendRow() {
    final Uuid _uuid = Uuid();
    var list = List.generate(5, (index) {
      return {'nodeId': _uuid.v4(), 'name': '777-new', 'pid': null, 'sex': '1', 'count': '50'};
    });

    var nextList = _stateManage.convertToTreeData(list).toList();

    _stateManage.appendRows(nextList);
  }

  void genRows(int rowCount) {
    _stateManage.setLoading(true);
    fetchData()
        .then((result) {
          _stateManage.setLoading(false);

          _handleAopendRow();
        })
        .catchError((err) {
          print(err);
        });
  }

  void loadMore() {
    if (!_stateManage.loadingMore) {
      if (_stateManage.hasMore) {
        // print('加载更多数据中');
        _stateManage.setLoadingMore(true);
        fetchData()
            .then((result) {
              _stateManage.setLoadingMore(false);

              _handleAopendRow();

              if (_stateManage.rows.length >= 30) {
                _stateManage.setHasMore(false);
                showMessage('没有更多数据了');
              }
            })
            .catchError((err) {
              print(err);
            });
      } else {
        showMessage('没有更多数据了');
      }
    }
  }

  /// 展示错误信息（替换成 tooltip 组件）
  void showMessage(String msg) {
    AppTableGeneralHelper.showMessage(msg);
  }

  // Future<Album> fetchAlbum() async {
  //   final response = await http.get(
  //     Uri.parse(
  //       'http://**************:7001/api/Business/SmartTableHeader/Edit',
  //     ),
  //   );

  //   if (response.statusCode == 200) {
  //     // If the server did return a 200 OK response,
  //     // then parse the JSON.
  //     return Album.fromJson(jsonDecode(response.body) as Map<String, dynamic>);
  //   } else {
  //     // If the server did not return a 200 OK response,
  //     // then throw an exception.
  //     throw Exception('Failed to load album');
  //   }
  // }

  final String _token =
      "tCLQz+0RWr9GGU1b43xolDcdnBBZxLZFxfg9QszX6Wem9LbM34dsIe1qcLLuM1K4tjZAH86g8nvUdKkEj0Pt9d+3O3FTMatVTMKaPecZ7DXBpp/9PsfGGNHg5FqujNxLvZz/Dhrx1IBFVpo34uqBSCJnqG1091Vmc/XtLwUhsbEZvhovWl+sTzZIZy1cdjnLXHPSc4Q/QEm0DyTo3lxu1S/9C5BoZOV2vBWiwzpJtEs=";

  BaseOptions _getOption() {
    const String APPLICATION_JSON = "application/json; charset=utf-8";

    return BaseOptions(
      connectTimeout: Duration(seconds: 10),
      receiveTimeout: Duration(seconds: 10),
      headers: {'Content-Type': APPLICATION_JSON, 'Accept': APPLICATION_JSON, 'token': _token},
      validateStatus: (status) {
        // 允许所有状态码，手动处理错误
        return status != null && status < 500;
      },
    );
  }

  /// 获取表格集合
  /// 获取表格集合
  /// 直接反序列化为 AppTableColumnsModel 集合
  Future<void> getTabs() async {
    try {
      dio.options = _getOption();
      var response = await dio.post(
        'http://**************:7001/api/Business/SmartTableHeader/GetListPage',
        data: {"PageSize": 10000, "PageIndex": 1},
      );

      if (response.statusCode == 200) {
        showMessage('请求表格列表————————成功');

        var jsonList = response.data['TMessageData']['Items'];

        if (jsonList is! List) {
          throw Exception('Items 不是数组格式');
        }

        // 直接转换为 AppTableColumnsModel 集合
        List<AppTableColumnsModel> tableModels =
            jsonList
                .map((item) => _parseAppTableModel(item))
                .where((model) => model != null)
                .cast<AppTableColumnsModel>()
                .toList();

        var result = tableModels.map((t) => t.smartTableHeaderId).toList();

        print(result);

        setState(() {
          tables = result;
        });
      } else {
        showMessage('请求表格列表————————失败 ${response.statusCode}');
      }
    } catch (e) {
      print('getTabs 异常: $e');
      showMessage('获取表格配置失败: $e');
    }
  }

  /// 解析单个 AppTableColumnsModel
  AppTableColumnsModel? _parseAppTableModel(Map<String, dynamic> data) {
    try {
      // 预处理数据，处理 HeaderSet 字符串
      Map<String, dynamic> processedData = Map<String, dynamic>.from(data);

      var headerSetData = data['HeaderSet'];

      if (headerSetData is String) {
        // 如果是字符串，解析为 List
        List<dynamic> headerSetList = jsonDecode(headerSetData);
        processedData['HeaderSet'] = headerSetList;
      } else if (headerSetData is! List) {
        // print('HeaderSet 数据格式不支持: ${headerSetData.runtimeType}');
        return null;
      }

      // 确保有 smartTableHeaderId 字段
      if (!processedData.containsKey('smartTableHeaderId')) {
        processedData['smartTableHeaderId'] = processedData['SmartTableHeaderId'] ?? '';
      }

      // 确保有 headerSet 字段
      if (!processedData.containsKey('headerSet')) {
        processedData['headerSet'] = processedData['HeaderSet'] ?? [];
      }

      // print('处理后的数据: $processedData');

      return AppTableColumnsModel.fromJson(processedData);
    } catch (e) {
      // print('解析 AppTableColumnsModel 失败: $e');
      // print('原始数据: $data');
      return null;
    }
  }

  Future<void> clearDb() async {
    var request1 = dio.post('http://**************:7001/api/Business/SmartTableHeader/Delete');
    var request2 = dio.post('http://**************:7001/api/Business/SmartTableBody/Delete');

    var completer = Completer<List<dynamic>>();
    await Future.wait([request1, request2]).then((results) {
      var response1 = results[0];
      var response2 = results[1];

      completer.complete([response1, response2]);
    });

    var results = await completer.future;

    showMessage('删除数据表成功~~~~~~~~~~~~~~~~~~~~~');
  }

  /// 编辑列
  /// [colsJsonDatas] 列数据
  /// [rowsJsonDatas] 行数据
  Future<void> editColumn(
    Map<String, String?> colsJsonDatas,
    Map<String, String?> rowsJsonDatas,
  ) async {
    try {
      // 配置 Dio
      dio.options = _getOption();

      var request1 = dio.post(
        'http://**************:7001/api/Business/SmartTableHeader/Edit',
        data: colsJsonDatas,
      );

      var request2 = dio.post(
        'http://**************:7001/api/Business/SmartTableBody/Edit',
        data: rowsJsonDatas,
      );
      var completer = Completer<List<dynamic>>();
      await Future.wait([request1, request2]).then((results) {
        var response1 = results[0];
        var response2 = results[1];

        completer.complete([response1, response2]);
      });

      var results = await completer.future;

      var response1 = results[0];
      var response2 = results[1];

      if (response1.statusCode == 200 || response2.statusCode == 200) {
        getTabs();
        showMessage('保存数据入库————成功');
      } else {
        // 处理具体的错误信息
        String errorMsg = '保存数据入库————失败';

        if (response1.data != null) {
          try {
            var errorData = response1.data;
            if (errorData is Map && errorData.containsKey('message')) {
              errorMsg = '保存失败: ${errorData['message']}';
            } else if (errorData is String) {
              errorMsg = '保存失败: $errorData';
            }
          } catch (e) {
            errorMsg = '保存失败: HTTP ${response1.statusCode}';
          }
        }

        showMessage(errorMsg);
        throw Exception('HTTP ${response1.statusCode}: $errorMsg');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getDetails(String smartTableHeaderId) async {
    dio.options = _getOption();
    var request1 = dio.get(
      'http://**************:7001/api/Business/SmartTableHeader/GetDetails',
      queryParameters: {"id": smartTableHeaderId},
    );

    var request2 = dio.get(
      'http://**************:7001/api/Business/SmartTableBody/GetDetailsByHeaderId',
      queryParameters: {"id": smartTableHeaderId},
    );

    // var request2 = dio.post(
    //   'http://**************:7001/api/Business/SmartTableBody/GetListPage',
    //   data: {"PageSize": 10000, "PageIndex": 1},
    // );

    var completer = Completer<List<dynamic>>();
    _stateManage.setLoading(true);
    await Future.wait([request1, request2]).then((results) {
      var response1 = results[0];
      var response2 = results[1];

      completer.complete([response1, response2]);
    });

    var results = await completer.future;

    var response1 = results[0];
    var response2 = results[1];

    _stateManage.setLoading(false);

    if (response1.statusCode == 200 || response2.statusCode == 200) {
      /// 还原列
      var jsonCol = response1.data['TMessageData'];
      AppTableColumnsModel? colModel = _parseAppTableModel(jsonCol);

      /// 还原行
      var cellOfRow = response2.data['TMessageData']['BodySet'];

      List<Map<String, dynamic>> jsonRowsStr = jsonDecode(cellOfRow).cast<Map<String, dynamic>>();
      var rows = _stateManage.convertToTreeData(jsonRowsStr);

      if (colModel != null) {
        currentTabId = smartTableHeaderId;
        _stateManage.setColumns(colModel!.headerSet);
        _stateManage.setRows(rows);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Wrap(
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    child: Text('加载columns、rows'),
                    onPressed: () {
                      getList(true);
                    },
                  ),

                  SizedBox(width: 20),

                  ElevatedButton(
                    child: Text('加载rows'),
                    onPressed: () {
                      getList(false);
                    },
                  ),

                  SizedBox(width: 20),

                  ElevatedButton(
                    child: Text('追加 5 条数据'),
                    onPressed: () {
                      genRows(5);
                    },
                  ),

                  SizedBox(width: 20),

                  ElevatedButton(
                    child: Text('加载更多'),
                    onPressed: () {
                      // loadMore();
                    },
                  ),

                  SizedBox(width: 20),

                  ElevatedButton(
                    child: Text('清空columns、rows'),
                    onPressed: () {
                      clearData(true);
                    },
                  ),

                  SizedBox(width: 20),

                  ElevatedButton(
                    child: Text('清空rows'),
                    onPressed: () {
                      clearData(false);
                    },
                  ),

                  SizedBox(width: 20),

                  ElevatedButton(
                    child: Text('显示loading'),
                    onPressed: () {
                      _stateManage.setLoading(true);
                    },
                  ),

                  SizedBox(width: 20),

                  ElevatedButton(
                    child: Text('隐藏loading'),
                    onPressed: () {
                      _stateManage.setLoading(false);
                    },
                  ),

                  ElevatedButton(
                    child: Text('测试数据转换'),
                    onPressed: () {
                      var list = [
                        {"id": "111", "name": "xzl", "Address": 123},
                      ];

                      String colsJson = jsonEncode(list);

                      List<dynamic> jsonColsStr = jsonDecode(colsJson);

                      List<Map<String, dynamic>> typedList =
                          jsonColsStr.cast<Map<String, dynamic>>();
                    },
                  ),

                  ElevatedButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              title: Text('确认删除'),
                              content: Text(
                                _stateManage.treeConfig != null &&
                                        _stateManage.hasChildren(_stateManage.rows[0]['nodeId'])
                                    ? '删除此节点将同时删除所有子节点，确认删除吗？'
                                    : '确认删除此行吗？',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: Text('取消'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    var id = _stateManage.rows[0]['nodeId'];
                                    Navigator.pop(context);
                                    // 执行删除
                                    _stateManage.removeRow(id);
                                  },
                                  child: Text('删除'),
                                ),
                              ],
                            ),
                      );
                    },
                    child: Text('删除第一行节点'),
                  ),

                  ElevatedButton(
                    child: Text('保存行、列json数据'),
                    onPressed: () {
                      var cols = _stateManage.columns;

                      // 集合转json
                      String colsJson = jsonEncode(cols);
                      String rowsJson = jsonEncode(
                        _stateManage.allRows.map((r) => r.data).toList(),
                      );

                      setState(() {
                        oldColsJson = colsJson;
                        oldRowsJson = rowsJson;
                      });
                      // print(oldColsJson);
                      // print(oldRowsJson);

                      ////////////////////////////////////////////// 调用接口保存列数据
                      var postColsDatas = {'SmartTableHeaderId': null, 'HeaderSet': colsJson};
                      var postRowsDatas = {'SmartTableBodyId': null, 'BodySet': rowsJson};

                      // print('存储数据');
                      // print(postColsDatas);

                      // editColumn(postColsDatas, postRowsDatas);

                      // getTabs();
                    },
                  ),
                  ElevatedButton(
                    child: Text('通过json数据还原表格行、列'),
                    onPressed: () {
                      //通过json还原列
                      List<dynamic> jsonColsStr = jsonDecode(oldColsJson);
                      List<AppTableColumn> cols =
                          jsonColsStr
                              .map((json) => AppTableColumn.fromJson(json as Map<String, dynamic>))
                              .toList();

                      //通过json还原行
                      List<Map<String, dynamic>> jsonRowsStr =
                          jsonDecode(oldRowsJson).cast<Map<String, dynamic>>();
                      var rows = _stateManage.convertToTreeData(jsonRowsStr);

                      _stateManage.setLoading(true);
                      fetchData()
                          .then((result) {
                            _stateManage.setLoading(false);
                            _stateManage.setColumns(cols);
                            _stateManage.setRows(rows);
                          })
                          .catchError((err) {});
                    },
                  ),

                  ElevatedButton(
                    child: Text('获取选中的行'),
                    onPressed: () {
                      print('一共选中：${_stateManage.checkedRows.length}');
                    },
                  ),
                  // ElevatedButton(
                  //   child: Text('模拟通过列json还原列数据'),
                  //   onPressed: () {
                  //     var cols = _stateManage.columns;

                  //     // 集合转json
                  //     String jsonString = jsonEncode(cols);
                  //     print(jsonString);

                  //     var jsonCols =
                  //         '[{"field":"name","text":"部门名称","typeCode":"text","type":{"defaultValue":"","isSaveRequired":false,"isSubmitRequired":false,"columnDesc":""},"width":300.0,"minWidth":100.0,"sortable":false,"resizable":true,"frozen":"start"},{"field":"sex","text":"性别","typeCode":"text","type":{"defaultValue":"","isSaveRequired":false,"isSubmitRequired":false,"columnDesc":""},"width":150.0,"minWidth":100.0,"sortable":false,"resizable":true,"frozen":"none"},{"field":"manager","text":"负责人","typeCode":"text","type":{"defaultValue":"","isSaveRequired":false,"isSubmitRequired":false,"columnDesc":""},"width":150.0,"minWidth":100.0,"sortable":false,"resizable":true,"frozen":"none"},{"field":"count","text":"人数","typeCode":"text","type":{"defaultValue":0,"isSaveRequired":false,"isSubmitRequired":false,"columnDesc":"","negative":false,"allowFirstDot":false,"locale":null,"format":"#,###","applyFormatOnInit":true,"precision":0,"isShowPercentiles":false},"width":200.0,"minWidth":100.0,"sortable":false,"resizable":true,"frozen":"none"},{"field":"b66431c3-3186-4a5a-9e13-4b67ca5f1a61","text":"文本字段","typeCode":"text","type":{"defaultValue":"1212","isSaveRequired":true,"isSubmitRequired":true,"columnDesc":""},"width":200.0,"minWidth":100.0,"sortable":false,"resizable":true,"frozen":"none"}]';

                  //     //json 数组转集合
                  //     List<dynamic> jsonList = jsonDecode(jsonCols);
                  //     List<AppTableColumn> cols2 =
                  //         jsonList
                  //             .map(
                  //               (json) => AppTableColumn.fromJson(
                  //                 json as Map<String, dynamic>,
                  //               ),
                  //             )
                  //             .toList();
                  //     print(cols2);

                  //     _stateManage.setLoading(true);
                  //     fetchData()
                  //         .then((result) {
                  //           _stateManage.setLoading(false);
                  //           _stateManage.setColumns(cols2);
                  //         })
                  //         .catchError((err) {});
                  //   },
                  // ),
                ],
              ),
            ),
            Expanded(
              child: Wrap(
                children: [
                  ElevatedButton(
                    child: Text('删除数据库所有表格列、行数据'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      clearDb();
                      getTabs();
                    },
                  ),

                  ElevatedButton(
                    child: Text('创建表格'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      //清空表格内容
                      clearData(true);

                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        //表格主键
                        currentTabId = Uuid().v4();
                        //添加默认新列
                        var newCol = AppTableGeneralHelper.newColumn(
                          type: ColumnTypeEnum.text,
                          text: '文本',
                        );
                        //表格列对象
                        var tableModel = AppTableColumnsModel(
                          smartTableHeaderId: currentTabId,
                          headerSet: [newCol],
                        );
                        //为表格设置默认列
                        _stateManage.setColumns(tableModel.headerSet);
                        //为表格添加默认行
                        _stateManage.addRow(parentId: null);

                        // var orgRow = [_stateManage.newRow(cols: tableModel.headerSet)];

                        // var rows = _stateManage.convertToTreeData(orgRow);

                        // _stateManage.setLoading(true);
                        // fetchData()
                        //     .then((result) {
                        //       _stateManage.setLoading(false);
                        //       _stateManage.setColumns(cols);
                        //       _stateManage.setRows(rows);
                        //     })
                        //     .catchError((err) {});
                      });
                    },
                  ),

                  ElevatedButton(
                    child: Text('保存当前表格数据'),
                    onPressed: () {
                      var cols = _stateManage.columns;
                      var rows = _stateManage.allRows.map((r) => r.data).toList();

                      // // 集合转json
                      // String colsJson = jsonEncode(cols);
                      // String rowsJson = jsonEncode(
                      //   _stateManage.allRows.map((r) => r.data).toList(),
                      // );

                      ////////////////////////////////////////////// 调用接口保存列数据
                      var postColsDatas = {
                        'SmartTableHeaderId': currentTabId,
                        'HeaderSet': jsonEncode(cols),
                      };
                      var postRowsDatas = {
                        // 'SmartTableBodyId': currentTabId,
                        'SmartTableHeaderId': currentTabId,
                        'BodySet': jsonEncode(rows),
                      };

                      print('保存————列数据：');
                      print('${postColsDatas}');
                      print('保存————行数据：');
                      print('${postRowsDatas}');
                      // print('currentTabId=====${currentTabId}');

                      editColumn(postColsDatas, postRowsDatas);
                    },
                  ),

                  ElevatedButton(
                    child: Text('获取所有表格'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      getTabs();
                    },
                  ),

                  Container(
                    height: 100,
                    child: ListView.builder(
                      itemCount: tables.length,
                      itemBuilder: (BuildContext context, int index) {
                        return ListTile(
                          title: Text('${index}——${tables[index]}'),
                          trailing: ElevatedButton(
                            child: Text('还原当前表格数据'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            onPressed: () {
                              clearData(true);
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                getDetails(tables[index]);
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        Expanded(
          child: Padding(
            padding: EdgeInsets.only(top: 0, right: 40, bottom: 20, left: 40),
            child: AppTable(
              // key: ValueKey(currentTabId),
              // loading: isLoading,
              // columns: columns,
              // rows: rows,
              // columns: [],
              // rows: [],
              checkType: TableCheckedEnum.none,
              indexColumnWidth: 100,
              defaultExpandAll: true,
              maxLevel: 6,

              // uniqueId: 'nodeId',

              /// 树形结构配置（扁平结构不需要传递树形结构配置参数）
              treeConfig: TreeTableConfig(
                idField: 'nodeId',
                parentIdField: 'pid',
                emptyParentValue: null,
              ),
              showAddRowButton: true,
              showAddColumnButton: true,
              // listviewPddingBottom: 0,

              /// 索引列自定义
              // indexCellBuilder: (context, index) {
              //   return Text('第${index}条');
              // },
              onCellTap: (context, rowIndex, columnIndex, value) {
                String msg = '点击了第${rowIndex}行，第${columnIndex}列，值为：$value';
                print(msg);
              },
              onLoaded: (stateManage) {
                _stateManage = stateManage;
                getList(true);
              },
              onLoadMore: () {
                // loadMore();
              },

              // onAddColumn: (
              //   context,
              //   AppTableColumn column,
              //   AppTableStateManage stateManager,
              //   double tableViewportWidth,
              // ) {
              //   print('添加列：${column}');

              //   if (columns.length < 10) {
              //     setState(() {
              //       //添加新列
              //       columns.add(column);

              //       //新增列的唯一标识符
              //       var newField = column.field;

              //       print(newField);

              //       // //所有的行补充新增列属性
              //       // for (var row in rows) {
              //       //   row.data[newField] = '';
              //       // }
              //     });

              //     // WidgetsBinding.instance.addPostFrameCallback((_) {
              //     //   //直接使用传入的状态管理实例执行滚动调整逻辑
              //     //   stateManager.adjustScrollPositionAfterAddColumn(
              //     //     tableViewportWidth,
              //     //     column.width,
              //     //   );
              //     // });
              //   } else {
              //     print('表格列不能超过10列-------------------');
              //   }

              //   // print('添加了新列：${column.field} ${column.text} ${column.width}');

              //   // AppTableGeneralHelper.newColumn(1000);
              // },
            ),
          ),
        ),
      ],
    );
  }
}
