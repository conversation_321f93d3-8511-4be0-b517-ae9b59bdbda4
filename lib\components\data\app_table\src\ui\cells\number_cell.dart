import 'package:flutter/cupertino.dart';
import 'package:octasync_client/components/data/app_table/src/app_number.dart';
import 'package:octasync_client/components/data/app_table/src/enums/cell_state_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_common.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_number_format.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_currency.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_percentage.dart';
import 'package:intl/intl.dart';

class NumberCell<T extends AppTableColumnTypeWithNumberFormat> extends StatefulWidget {
  const NumberCell({
    super.key,
    this.state = CellStateEnum.normal,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final CellStateEnum state;

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;
  final String rowId;
  final String columnId;

  @override
  State<NumberCell<T>> createState() => _NumberCellState<T>();
}

class _NumberCellState<T extends AppTableColumnTypeWithNumberFormat> extends State<NumberCell<T>> {
  late AppTableColumn column;
  late T columnObj;
  late Map<String, dynamic> rowData;
  late String rowId;
  late String columnId;

  @override
  void initState() {
    super.initState();
    column = widget.column;
    columnObj = column.type as T;
    rowData = widget.rowData;
    rowId = widget.rowId;
    columnId = widget.columnId;
  }

  @override
  Widget build(BuildContext context) {
    var value = rowData[column.field];

    var isNumber = columnObj is AppTableColumnTypeNumber;
    var isPercentage = columnObj is AppTableColumnTypePercentage;
    var isCurrency = columnObj is AppTableColumnTypeCurrency;

    var precision = 0;
    var isShowPercentiles = false;
    var negative = false;
    var isRetainDecimal = true;
    var prefix = '';
    var suffix = '';

    if (isNumber) {
      var obj = columnObj as AppTableColumnTypeNumber;
      precision = obj.precision;
      isShowPercentiles = obj.isShowPercentiles;
      negative = obj.negative;
      isRetainDecimal = obj.isRetainDecimal;
    } else if (isPercentage) {
      var obj = columnObj as AppTableColumnTypePercentage;
      precision = obj.precision;
      isShowPercentiles = obj.isShowPercentiles;
      negative = obj.negative;
      isRetainDecimal = obj.isRetainDecimal;
      suffix = '%';
    } else if (isCurrency) {
      var obj = columnObj as AppTableColumnTypeCurrency;
      isShowPercentiles = obj.isShowPercentiles;
      precision = obj.precision;
      negative = obj.negative;
      isRetainDecimal = obj.isRetainDecimal;

      var typeOption = AppTableCommon.getCurrencyType(obj.currencyType);
      if (typeOption != null) {
        prefix = typeOption.symbol ?? '';
      }
    }

    num? number;

    if (value is num) {
      number = value;
    } else if (value is String) {
      number = num.tryParse(value);
    } else if (value == null) {
      number = null;
    }

    if (number != null) {
      var value = rowData[column.field];
      var state = widget.state;

      if (state == CellStateEnum.edit) {
        return AppNumber(
          initialValue: value == null ? null : double.tryParse(value.toString()),
          precision: precision,
          negative: negative,
          isRetainDecimal: isRetainDecimal,
          isShowPercentiles: isShowPercentiles,
          prefix: prefix,
          suffix: suffix,
          onChanged: (value) {
            rowData[column.field] = value.toString();
          },

          //
          //

          // onChanged: (value) {
          //    rowData[column.field] = value;
          // },
        );
      } else {
        var numFlag = columnObj.numberFormat.format(number).toString();
        if (isPercentage) {
          numFlag += '%';
        } else if (isCurrency) {
          var currencyType = (columnObj as AppTableColumnTypeCurrency).currencyType;
          var option = AppTableCommon.getCurrencyType(currencyType);
          numFlag = ('${option != null ? option.symbol : ''}${numFlag}');
        }
        return Text(numFlag);
      }
    } else {
      // 处理空值或无效值的情况
      return Text('');
    }
  }
}
