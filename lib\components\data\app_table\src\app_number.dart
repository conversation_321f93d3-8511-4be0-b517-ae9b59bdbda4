import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:octasync_client/components/data/app_table/src/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:intl/intl.dart' show NumberFormat;

class AppNumber<T extends num> extends StatefulWidget {
  /// 输出值
  final T? initialValue;

  /// 返回修改后的值
  final ValueChanged<T?>? onChanged;

  /// 小数位数
  final int precision;

  /// 是否允许负数
  final bool negative;

  /// 是否显示千分位
  final bool isShowPercentiles;

  /// 强制保留小数数位（不足补0）
  final bool isRetainDecimal;

  // final InputDecoration? decoration;

  /// 后缀
  final String prefix;

  /// 后缀
  final String suffix;

  const AppNumber({
    super.key,
    this.initialValue,
    this.onChanged,
    this.precision = 0,
    this.negative = false,
    this.isShowPercentiles = false,
    this.isRetainDecimal = false,
    // this.decoration,
    this.prefix = '',
    this.suffix = '',
  });

  @override
  State<AppNumber<T>> createState() => _AppNumberState<T>();
}

var decoration = InputDecoration(
  border: OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1)),
  contentPadding: EdgeInsets.symmetric(horizontal: 10),
  hintText: '',
);

class _AppNumberState<T extends num> extends State<AppNumber<T>> {
  late String _format;

  @override
  void initState() {
    super.initState();

    _initFormat();
  }

  @override
  void didUpdateWidget(covariant AppNumber<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    // print('触发：didUpdateWidget');
    // print('oldWidget.isRetainDecimal====${oldWidget.isRetainDecimal}');
    // print('widget.isRetainDecimal====${widget.isRetainDecimal}');

    // 当 widget 参数发生变化时，重新计算格式
    if (oldWidget.isShowPercentiles != widget.isShowPercentiles ||
        oldWidget.isRetainDecimal != widget.isRetainDecimal ||
        oldWidget.precision != widget.precision ||
        oldWidget.prefix != widget.prefix ||
        oldWidget.suffix != widget.suffix) {
      _updateFormat();
    }
  }

  void _updateFormat() {
    _initFormat();
    setState(() {});
  }

  void _initFormat() {
    _format = AppTableGeneralHelper.getFormatStr(
      isShowPercentiles: widget.isShowPercentiles,
      precision: widget.precision,
      isRetainDecimal: widget.isRetainDecimal,
    );
  }

  @override
  Widget build(BuildContext context) {
    // 展示（失去焦点）时格式
    final NumberFormat normalFormat = NumberFormat(_format, "en_US"); //#,##0.####

    // 输入（获取光标）时格式
    final NumberFormat editFormat = NumberFormat("0.########", "en_US");

    /// 是否为整数（不允许小数点）
    var isInteger = widget.precision == 0;

    /// 是否可以为负数
    var isNegative = widget.negative;

    /// [0-9.-]
    var testReg = "[0-9${!isInteger ? '.' : ''}${isNegative ? '-' : ''}]";

    return AppBasicTextField(
      //设置key，通过key来刷新当前组件，不然修改 isPercentiles、isRetainDecimal 后，显示数据不变
      key: ValueKey('format_${_format}_${widget.prefix}_${widget.suffix}'),
      // normalFormatString: _format,
      initialValue: widget.initialValue?.toString(),
      //数字键盘（precision == 0 表示整数）
      keyboardType: TextInputType.numberWithOptions(decimal: !isInteger),
      inputFormatters: [
        FilteringTextInputFormatter(RegExp(testReg), allow: true),
        LengthLimitingTextInputFormatter(15), //限制长度
        MyNumberTextInputFormatter(decimalDigits: widget.precision), //小数位数
      ],
      formatOnFocus: (value) {
        if (value is num) {
          var result = editFormat.format(value);
          return result;
        }
        return value.toString();
      },
      formatOnBlur: (value) {
        var result = value;
        if (value is num) {
          result = normalFormat.format(value);
        }

        var _prefix = widget.prefix.isNotEmpty ? widget.prefix : '';
        var _suffix = widget.suffix.isNotEmpty ? widget.suffix : '';
        return '${_prefix}${result.toString()}${_suffix}';
      },
      parser: (text) {
        // 移除千分位分隔符并解析为数字
        String cleanText = text.replaceAll(',', '');
        if (widget.prefix.isNotEmpty) {
          cleanText = cleanText.replaceAll(widget.prefix, '');
        }
        if (widget.suffix.isNotEmpty) {
          cleanText = cleanText.replaceAll(widget.suffix, '');
        }

        if (cleanText.isEmpty) return null;

        var result =
            isInteger
                ? (double.tryParse(cleanText)?.toInt() ?? 0)
                : (double.tryParse(cleanText) ?? 0.0);
        return result;
      },

      onParsedChanged: (value) {
        // 确保总是传递 double 类型
        if (widget.onChanged != null) {
          if (value.isEmpty) {
            widget.onChanged!(null);
          } else {
            T? result;

            if (!isInteger) {
              double parsedValue =
                  value is double
                      ? double.parse(value)
                      : value is num
                      ? double.parse(value)
                      : (double.tryParse(value.toString()) ?? 0);
              result = parsedValue as T;
            } else {
              int parsedValue =
                  value is int
                      ? int.parse(value)
                      : value is num
                      ? int.parse(value)
                      : (int.tryParse(value.toString()) ?? 0);

              result = parsedValue.toDouble() as T;
            }

            widget.onChanged!(result);
          }
        }
      },
      decoration: decoration,
    );
  }
}

class MyNumberTextInputFormatter extends TextInputFormatter {
  static const defaultDouble = 0.001;

  ///允许的小数位数，-1代表不限制位数
  int decimalDigits;
  MyNumberTextInputFormatter({this.decimalDigits = -1});
  static double strToFloat(String str, [double defaultValue = defaultDouble]) {
    try {
      return double.parse(str);
    } catch (e) {
      return defaultValue;
    }
  }

  ///获取目前的小数位数
  static int getValueDigit(String value) {
    if (value.contains(".")) {
      return value.split(".")[1].length;
    } else {
      return -1;
    }
  }

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String value = newValue.text;
    int selectionIndex = newValue.selection.end;
    if (value == ".") {
      value = "0.";
      selectionIndex++;
    } else if (value == "-") {
      value = "-";
      selectionIndex++;
    } else if (value != "" &&
            value != defaultDouble.toString() &&
            strToFloat(value, defaultDouble) == defaultDouble ||
        getValueDigit(value) > decimalDigits) {
      value = oldValue.text;
      selectionIndex = oldValue.selection.end;
    }
    return new TextEditingValue(
      text: value,
      selection: new TextSelection.collapsed(offset: selectionIndex),
    );
  }
}
