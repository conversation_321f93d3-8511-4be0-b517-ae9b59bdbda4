class AuthGroupModel {
  bool isExpanded;
  bool? isChecked;
  String label;
  String? tip;
  List<AuthItem> items;
  AuthGroupModel({
    this.isExpanded = true,
    this.isChecked = false,
    required this.label,
    this.tip,
    List<AuthItem>? items,
  }) : items = items ?? [];
}

class AuthItem {
  int authId;
  bool isChecked;
  String label;
  String? tip;

  AuthItem({required this.authId, this.isChecked = false, required this.label, this.tip});
}
