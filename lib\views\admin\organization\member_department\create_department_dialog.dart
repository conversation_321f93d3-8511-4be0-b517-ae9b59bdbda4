import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/components/selector/employee_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';

/// 弹窗类型
enum DialogTypeEmun { create, edit }

class CreateDepartmentDialog extends StatefulWidget {
  final void Function()? onSuccess; // 提交成功回调
  final void Function()? onError; // 提交失败回调

  const CreateDepartmentDialog({super.key, this.onSuccess, this.onError});

  @override
  State<CreateDepartmentDialog> createState() => DepartmentDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class DepartmentDialogState extends State<CreateDepartmentDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  DepartmentModel _departmentModel = DepartmentModel();

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  /// 选中的部门列表
  List<DepartmentModel> _checkedDepartments = [];

  /// 选中的部门负责人
  List<Employee> _checkedPrincipalEmployeeList = [];

  /// 选中的部门HRBP
  List<Employee> _checkedDepHRBPEmployeeList = [];

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _depNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  /// 重置数据
  void resetFormData() {
    _departmentModel = DepartmentModel();
    _departmentModel.departmentName = '';
    _depNameController.text = '';
    _descriptionController.text = '';
    _checkedDepartments = [];
    _checkedPrincipalEmployeeList = [];
    _checkedDepHRBPEmployeeList = [];
  }

  /// 添加
  Future<void> createRequest(BuildContext context, StateSetter setDialogState) async {
    if (!_formKey.currentState!.validate()) return;

    _departmentModel.parentIdList = _checkedDepartments.map((t) => t.id!).toList();
    final params = _departmentModel.toJson();

    if (_dialogType == DialogTypeEmun.create) {
      params.remove('Id');
    }

    try {
      setDialogState(() {
        btnLoading = true;
      });
      const apiMap = {
        DialogTypeEmun.create: DepartmentApi.add,
        DialogTypeEmun.edit: DepartmentApi.edit,
      };
      await apiMap[_dialogType]!(params);

      ToastManager.success('提交成功');
      widget.onSuccess?.call();
      resetFormData();
      // 创建模式且不继续添加，或编辑模式，则关闭弹窗
      if (mounted && (_dialogType == DialogTypeEmun.edit || !isAddNext)) {
        Navigator.of(context).pop();
      }
    } finally {
      setDialogState(() {
        btnLoading = false;
      });
    }
  }

  /// 通过详情接口获取并填充编辑数据
  Future<void> fillEditData(String id) async {
    try {
      // 调用详情接口获取完整数据
      final response = await DepartmentApi.getDetails({'Id': id});

      if (response != null) {
        // 将响应数据转换为 PositionModel
        _departmentModel = DepartmentModel.fromJson(response);
        _depNameController.text = _departmentModel.departmentName;
        _descriptionController.text = _departmentModel.description;
        _checkedDepartments = _departmentModel.parentList;

        // 部门HRBP
        if (_departmentModel.departmentHrbpId != null) {
          _checkedDepHRBPEmployeeList = [
            Employee(
              employeeId: _departmentModel.departmentHrbpId,
              name: _departmentModel.departmentHrbpName ?? '',
            ),
          ];
        }

        // 部门负责人
        if (_departmentModel.departmentHeadList.isNotEmpty) {
          _checkedPrincipalEmployeeList = _departmentModel.departmentHeadList;
        }
      }
    } catch (e) {
      ToastManager.error('获取详情失败');
    }
  }

  /// 打开添加部门弹窗
  void showCreateDepartmentDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    String? id,
  }) async {
    // 重置表单数据
    resetFormData();

    // 如果是编辑模式，先加载数据再显示弹窗
    if (type == DialogTypeEmun.edit && id != null) {
      _departmentModel.id = id;
      await fillEditData(id);
    }

    // 检查组件是否仍然挂载
    if (!mounted) return;

    _dialogType = type;

    double labelWidth = 85;

    /// 部门名称
    Widget buildDepNameInput() {
      return AppInput(
        label: "部门名称",
        required: true,
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "部门名称",
        size: InputSize.medium,
        controller: _depNameController,
        maxLength: 30,
        validator: (value) {
          if (_departmentModel.departmentName.isEmpty) {
            return '请输入部门名称';
          }
          return null;
        },
        onChanged: (value) {
          _departmentModel.departmentName = value;
        },
      );
    }

    /// 上级部门
    Widget buildParentDepartment() {
      return AppFormField(
        label: '上级部门',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_checkedDepartments.isEmpty) {
            return '请选择部门';
          }
          return null;
        },
        builder:
            (field) => DepartmentSelector(
              checkStrictly: true,
              defaultCheckedDepartment: _checkedDepartments,
              onChange: (value) {
                field.didChange(value);
                _checkedDepartments = value;
              },
            ),
      );
    }

    /// 部门负责人
    Widget buildDepartmentManager() {
      return AppFormField(
        label: '部门负责人',
        required: false,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          return null;
        },
        builder:
            (field) => EmployeeSelector(
              defaultCheckedEmployeeList: _checkedPrincipalEmployeeList,
              maxSelectableEmployees: 1,
              onChange: (value) {
                field.didChange(value);
                _checkedPrincipalEmployeeList = value;
                _departmentModel.departmentHeadIdList = value.map((t) => t.employeeId!).toList();
              },
            ),
      );
    }

    /// 部门HRBP
    Widget buildDepartmentHRBP() {
      return AppFormField(
        label: '部门HRBP',
        required: false,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          return null;
        },
        builder:
            (field) => EmployeeSelector(
              defaultCheckedEmployeeList: _checkedDepHRBPEmployeeList,
              maxSelectableEmployees: 1,
              onChange: (value) {
                field.didChange(value);
                _checkedDepHRBPEmployeeList = value;
                _departmentModel.departmentHrbpId = value.firstOrNull?.employeeId;
              },
            ),
      );
    }

    /// 描述
    Widget buildDescription() {
      return AppInput(
        label: "职能描述",
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        controller: _descriptionController,
        maxLines: 5,
        hintText: "职能描述",
        size: InputSize.medium,
        maxLength: 3000,
        onChanged: (value) {
          _departmentModel.description = value;
        },
      );
    }

    AppDialog.show(
      width: 480,
      context: context,
      title: _dialogType == DialogTypeEmun.create ? '添加部门' : '编辑部门',
      isDrawer: true,
      barrierDismissible: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Column(
            children: [
              Expanded(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildDepNameInput(),
                      buildParentDepartment(),
                      buildDepartmentManager(),
                      buildDepartmentHRBP(),
                      buildDescription(),
                    ],
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 只有创建模式才显示"继续新建下一条"选项
                    if (_dialogType == DialogTypeEmun.create) ...[
                      Checkbox(
                        value: isAddNext,
                        onChanged: (value) {
                          setDialogState(() {
                            isAddNext = !isAddNext;
                          });
                        },
                      ),
                      Text('继续新建下一条'),
                      const SizedBox(width: 10),
                    ],
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      loading: btnLoading,
                      onPressed: () => createRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _depNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppButton(
      text: '添加部门',
      type: ButtonType.primary,
      loading: btnLoading,
      onPressed: () {
        showCreateDepartmentDialog(context, type: DialogTypeEmun.create);
      },
    );
  }
}
