import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 轮播图
class BannerSlider extends StatefulWidget {
  const BannerSlider({super.key});

  @override
  State<BannerSlider> createState() => _BannerSliderState();
}

class _BannerSliderState extends State<BannerSlider> {
  int _current = 0;

  // TODO:替换成服务端图片资源
  final List<Color> _bannerList = [Colors.amber, Colors.blue, Colors.green];

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 轮播图
        ClipRRect(
          borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
          child: CarouselSlider(
            items:
                _bannerList.map((item) {
                  return Builder(
                    builder: (BuildContext context) {
                      return Container(color: item);
                    },
                  );
                }).toList(),
            options: CarouselOptions(
              height: double.infinity,
              viewportFraction: 1.0,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 4),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              onPageChanged: (index, reason) {
                setState(() {
                  _current = index;
                });
              },
            ),
          ),
        ),
        // 计数器
        Positioned(
          bottom: 10,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 5,
            children: List.generate(
              _bannerList.length,
              (index) => Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      _current == index ? AppColors.white : AppColors.white.withValues(alpha: .4),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
