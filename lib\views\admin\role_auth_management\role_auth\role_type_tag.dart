import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

class RoleTypeTag extends StatelessWidget {
  ///1：系统；2：非系统
  final int roleType;

  const RoleTypeTag({super.key, this.roleType = 1});

  @override
  Widget build(BuildContext context) {
    return AppTag(
      text: roleType == 1 ? '系统' : '自定义',
      textColor: roleType == 1 ? AppColors.primary : AppColors.warning,
      color:
          roleType == 1
              ? AppColors.primary.withValues(alpha: 0.1)
              : AppColors.warning.withValues(alpha: 0.1),
    );
  }
}
