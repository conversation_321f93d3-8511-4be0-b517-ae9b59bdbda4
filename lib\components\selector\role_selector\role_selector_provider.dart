import 'package:flutter/material.dart';
import 'package:octasync_client/components/selector/role_selector/role_tree_model.dart';
import 'package:octasync_client/components/selector/role_selector/role_tree.dart';

/// 角色选择器状态管理 Provider
class RoleSelectorProvider extends ChangeNotifier {
  /// 当前选中的角色列表
  List<RoleTreeModel> _checkedRoles = [];

  /// 搜索关键词
  String _searchQuery = '';

  /// 默认选中的角色ID列表
  List<String> _defaultCheckedRoleIds = [];

  /// 最大可选择角色数量，为null时表示无限制
  int? _maxSelectableRoles;

  /// 获取当前选中的角色列表
  List<RoleTreeModel> get checkedRoles => List.unmodifiable(_checkedRoles);

  /// 获取当前搜索关键词
  String get searchQuery => _searchQuery;

  /// 获取选中角色数量
  int get checkedCount => _checkedRoles.length;

  /// 获取默认选中的角色ID列表
  List<String> get defaultCheckedRoleIds => List.unmodifiable(_defaultCheckedRoleIds);

  /// 获取最大可选择角色数量
  int? get maxSelectableRoles => _maxSelectableRoles;

  /// 检查是否可以添加更多角色
  bool get canAddMoreRoles {
    if (_maxSelectableRoles == null) return true;
    return _checkedRoles.length < _maxSelectableRoles!;
  }

  /// 检查是否已达到最大选择限制
  bool get isMaxSelectionReached {
    if (_maxSelectableRoles == null) return false;
    return _checkedRoles.length >= _maxSelectableRoles!;
  }

  /// 设置默认选中的角色ID列表
  void setDefaultCheckedRoleIds(List<String> roleIds) {
    _defaultCheckedRoleIds = List.from(roleIds);
    notifyListeners();
  }

  /// 设置最大可选择角色数量
  void setMaxSelectableRoles(int? maxCount) {
    _maxSelectableRoles = maxCount;
    // 如果当前选中数量超过新的限制，需要截断
    if (maxCount != null && _checkedRoles.length > maxCount) {
      _checkedRoles = _checkedRoles.take(maxCount).toList();
    }
    notifyListeners();
  }

  /// 检查角色ID是否在默认选中列表中
  bool isDefaultChecked(String roleId) {
    return _defaultCheckedRoleIds.contains(roleId);
  }

  /// 更新选中的角色列表
  void updateCheckedRoles(List<RoleTreeModel> roles) {
    _checkedRoles = List.from(roles);
    notifyListeners();
  }

  /// 添加选中的角色
  void addCheckedRole(RoleTreeModel role) {
    if (!_checkedRoles.any((e) => e.id == role.id)) {
      // 检查是否超过最大选择限制
      if (_maxSelectableRoles != null && _checkedRoles.length >= _maxSelectableRoles!) {
        return; // 不添加，已达到最大限制
      }
      _checkedRoles.add(role);
      notifyListeners();
    }
  }

  /// 移除选中的角色
  void removeCheckedRole(String roleId) {
    _checkedRoles.removeWhere((e) => e.id == roleId);
    notifyListeners();
  }

  /// 清空所有选中的角色
  void clearAllCheckedRoles() {
    _checkedRoles.clear();
    notifyListeners();
  }

  /// 设置搜索关键词
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// 重置状态
  void reset() {
    _checkedRoles.clear();
    _searchQuery = '';
    notifyListeners();
  }

  /// 重置到默认选中状态（保留默认选中的角色）
  void resetToDefault() {
    _checkedRoles.clear();
    _searchQuery = '';
    // 注意：这里不清空 _defaultCheckedRoleIds，因为它们应该保持
    notifyListeners();
  }

  /// 应用默认选中状态到角色树（数据加载完成后调用）
  void applyDefaultCheckedState(GlobalKey<RoleTreeState> treeKey) {
    if (_defaultCheckedRoleIds.isNotEmpty) {
      final treeState = treeKey.currentState;
      if (treeState != null) {
        // 先重置所有选中状态
        treeState.resetAllNodesCheck();
        // 然后设置默认选中的角色
        for (final roleId in _defaultCheckedRoleIds) {
          treeState.checkNode(roleId);
        }
        // 更新Provider中的选中列表
        final checkedRoles = treeState.getAllCheckedRoles();
        updateCheckedRoles(checkedRoles);
      }
    }
  }
}
