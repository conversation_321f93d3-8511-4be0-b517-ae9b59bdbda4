import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/src/enums/cell_state_enum.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_text.dart';
import 'package:octasync_client/components/data/app_table/src/state/app_table_state_manage.dart';
import 'package:provider/provider.dart';

class TextCell extends StatefulWidget {
  const TextCell({
    super.key,
    this.state = CellStateEnum.normal,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final CellStateEnum state;

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;

  final String rowId;
  final String columnId;

  @override
  State<TextCell> createState() => _TextCellState();
}

class _TextCellState extends State<TextCell> {
  late AppTableColumn column;
  late AppTableColumnTypeText columnObj;
  late Map<String, dynamic> rowData;
  late int rowIdx = 0;
  late int columnIdx = 0;
  late String rowId;
  late String columnId;

  var outlineInputBorder = OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1));

  @override
  void initState() {
    super.initState();
    column = widget.column;
    columnObj = column.type as AppTableColumnTypeText;
    rowData = widget.rowData;
    rowIdx = widget.rowIdx;
    columnIdx = widget.columnIdx;
    rowId = widget.rowId;
    columnId = widget.columnId;

    // print('111111=====${widget.rowIdx}-----${widget.columnIdx}');
  }

  @override
  Widget build(BuildContext context) {
    // print('${widget.rowIdx}-----${widget.columnIdx}====${column.field}---');
    // print('2222222=====${widget.columnId}---${widget.rowId}');

    var value = rowData[column.field];
    var state = widget.state;

    if (state == CellStateEnum.edit) {
      return AppBasicTextField(
        // controller: _nameController,
        initialValue: value,
        onChanged: (value) {
          rowData[column.field] = value;
          setState(() {});
        },
        decoration: InputDecoration(
          border: outlineInputBorder,
          contentPadding: EdgeInsets.symmetric(horizontal: 10),
          hintText: '请输入字段名称',
        ),
      );
    } else {
      return Text('${value ?? ''}');
    }
  }
}
