import 'package:flutter/material.dart';
import 'package:octasync_client/api/role_auth_management.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/role_mgmt/batch_assign.dart';
import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/dialog_type_enum.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/role_auth_var.dart';
import 'package:octasync_client/views/projects/components/assign_tab.dart';

/// 角色弹窗组件
class BatchAssignDialog extends StatefulWidget {
  final Widget? child;
  final void Function()? onSuccess; // 提交成功回调

  const BatchAssignDialog({super.key, this.child, this.onSuccess});

  @override
  State<BatchAssignDialog> createState() => BatchAssignDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class BatchAssignDialogState extends State<BatchAssignDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  BatchAssign batchAssign = BatchAssign();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final SelectController<int> _dataScopeenumSelectController = SelectController<int>();

  List<GroupConfig> groups = [];
  List<Role> roles = [];

  /// 选中的部门列表
  List<DepartmentModel> checkedDepartments = [];

  /// 加载中
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  /// 重置数据
  void resetFormData([StateSetter? setDialogState]) {
    batchAssign = BatchAssign();
    _dataScopeenumSelectController.clear();
    checkedDepartments = [];
    // if (setDialogState != null) {
    //   setDialogState(() {
    //     // isAddNext = false;
    //   });
    // }
  }

  // /// 通过详情接口获取并填充编辑数据
  Future<void> fillEditData(String id, StateSetter setDialogState) async {
    try {
      setDialogState(() {
        isLoading = true;
      });

      // 调用详情接口获取完整数据
      final response = await RoleAuthManagementApi.getRoleDetail({'Id': id});

      if (response != null) {
        // 将响应数据转换为 batchAssign
        batchAssign = BatchAssign.fromJson(response);

        _dataScopeenumSelectController.setValue(batchAssign.dataScopeenum);

        isLoading = false;
        setDialogState(() {});
      }
      // 填充表单数据
    } catch (e) {
      isLoading = false;
      setDialogState(() {});
      ToastManager.error('获取详情失败');
    }
  }

  /// 提交数据 - 支持创建和编辑
  Future<void> submitRequest(BuildContext context, StateSetter setDialogState) async {
    // 使用 Form 的校验功能
    if (!_formKey.currentState!.validate()) {
      ToastManager.error('请填写完整信息');
      return;
    }

    setDialogState(() {
      btnLoading = true;
    });

    try {
      const apiMap = {DialogTypeEmun.create: RoleAuthManagementApi.batchEditDataScope};

      await apiMap[_dialogType]!(batchAssign.toJson());

      ToastManager.success('操作成功');

      setDialogState(() {
        btnLoading = false;
      });

      // 只有创建模式才重置表单（编辑模式直接关闭）
      if (_dialogType == DialogTypeEmun.create) {
        resetFormData(setDialogState);
      }

      widget.onSuccess?.call();

      // 创建模式且不继续添加，或编辑模式，则关闭弹窗
      if (mounted && (_dialogType == DialogTypeEmun.edit || !isAddNext)) {
        Navigator.of(context).pop();
      }
    } catch (err) {
      setDialogState(() {
        btnLoading = true;
      });
    }
  }

  /// 打开角色弹窗 - 支持创建和编辑
  Future<void> showBatchAssignDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    List<String> roleIdList = const [],
    // String? id,
    // Role? role,
  }) async {
    _dialogType = type;

    // /// 详情是否已经加载（确保只能调用一次接口）
    // bool isDetailLoaded = false;

    // 重置表单数据
    resetFormData();

    batchAssign.roleIdList = roleIdList;

    // 检查组件是否仍然挂载
    if (!mounted) return;

    double labelWidth = 100;

    /// 数据管理范围（待处理）
    Widget buildDataScopeenumSelect(StateSetter setDialogState) {
      return AppFormField(
        label: '数据管理范围',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_dataScopeenumSelectController.value == null ||
              _dataScopeenumSelectController.value == 0) {
            return '请选择数据管理范围';
          }
          return null;
        },
        builder:
            (field) => AppSelect<int>(
              placeholder: '请选择',
              options: RoleAuthVar.dataScopeenums,
              controller: _dataScopeenumSelectController,
              onChanged: (value) {
                field.didChange(value.toString());
                setDialogState(() {
                  batchAssign.dataScopeenum = value!;
                });
              },
            ),
      );
    }

    // 部门选择
    Widget buildDepartmentSelect(StateSetter setDialogState) {
      return AppFormField(
        label: '部门',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (batchAssign.dataScopeenum == 5 && checkedDepartments.isEmpty) {
            return '请选择部门';
          }
          return null;
        },
        builder:
            (field) => DepartmentSelector(
              checkStrictly: true,
              defaultCheckedDepartment: checkedDepartments,
              onChange: (value) {
                field.didChange(value);
                setDialogState(() {
                  checkedDepartments = value;
                });
              },
            ),
      );
    }

    AppDialog.show(
      width: 500,
      height: 300,
      context: context,
      title: '批量指定数据范围',
      // isDrawer: true,
      // slideDirection: SlideDirection.right,
      showFooter: false,
      barrierDismissible: true,
      padding: EdgeInsetsGeometry.zero,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return AppLoading(
            isLoading: isLoading,
            child: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 10,
                        children: [
                          buildDataScopeenumSelect(setDialogState),

                          if (batchAssign.dataScopeenum == 5) buildDepartmentSelect(setDialogState),
                        ],
                      ),
                    ),
                  ),
                ),
                Divider(),
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      AppButton(
                        text: '取消',
                        type: ButtonType.default_,
                        onPressed: () => context.pop(),
                      ),
                      const SizedBox(width: 10),
                      AppButton(
                        text: '确定',
                        type: ButtonType.primary,
                        loading: btnLoading,
                        onPressed: () => submitRequest(context, setDialogState),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _dataScopeenumSelectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
