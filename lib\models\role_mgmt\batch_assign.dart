import 'package:json_annotation/json_annotation.dart';

part 'batch_assign.g.dart';

/// 批量指定数据范围模型
@JsonSerializable()
class BatchAssign {
  @JsonKey(name: 'DataScopeenum', defaultValue: 1)
  int dataScopeenum;

  @JsonKey(name: 'DepartmentIdList', defaultValue: [])
  List<String> departmentIdList;

  @JsonKey(name: 'RoleIdList', defaultValue: [])
  List<String> roleIdList;

  BatchAssign({
    this.roleIdList = const [],
    this.dataScopeenum = 1,
    this.departmentIdList = const [],
  });

  factory BatchAssign.fromJson(Map<String, dynamic> json) => _$Batch<PERSON>sign<PERSON>rom<PERSON>son(json);

  Map<String, dynamic> toJson() => _$BatchAssignToJson(this);
}
