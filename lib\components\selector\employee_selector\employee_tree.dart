import 'package:flutter/material.dart';
import 'package:octasync_client/api/employee.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree_model.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree_styles.dart';
import 'package:octasync_client/imports.dart';

// 常量定义
class _EmployeeTreeConstants {
  static const String loadingMessage = '正在加载人员数据...';
  static const String emptyDataMessage = '暂无人员数据';
  static const String retryButtonText = '重试';
  static const String loadErrorPrefix = '加载人员数据失败: ';
}

/// 人员树节点数据模型 - 包装 EmployeeTreeModel 并添加UI状态
class EmployeeTreeNode {
  final EmployeeTreeModel employee;
  final List<EmployeeTreeNode> children;
  bool isExpanded;
  bool isSelected; // 节点点击选择状态（高亮显示）
  bool isChecked; // 复选框选中状态
  bool isIndeterminate; // 复选框半选状态
  bool isVisible; // 搜索时节点是否可见
  bool isDisabled; // 是否禁用选择

  // 缓存计算结果以提高性能
  int? _cachedLevel;
  int? _cachedCheckedCount;

  EmployeeTreeNode({
    required this.employee,
    List<EmployeeTreeNode>? children,
    this.isExpanded = false,
    this.isSelected = false,
    this.isChecked = false,
    this.isIndeterminate = false,
    this.isVisible = true,
    this.isDisabled = false,
  }) : children = children ?? [];

  /// 检查是否为叶子节点
  bool get isLeaf => children.isEmpty;

  /// 获取节点层级深度（带缓存）
  int get level {
    _cachedLevel ??= _calculateLevel();
    return _cachedLevel!;
  }

  int _calculateLevel() {
    if (employee.parentId == null) return 0;
    // 简单计算层级，可以根据需要优化
    return employee.isDepartment ? 0 : 1;
  }

  /// 获取所有被选中的子节点数量（带缓存）
  int get checkedChildrenCount {
    _cachedCheckedCount ??= children.where((child) => child.isChecked).length;
    return _cachedCheckedCount!;
  }

  /// 清除缓存（当子节点状态变化时调用）
  void clearCache() {
    _cachedCheckedCount = null;
  }

  /// 获取所有子节点数量
  int get totalChildrenCount => children.length;

  /// 检查是否有部分子节点被选中
  bool get hasPartiallyCheckedChildren {
    if (children.isEmpty) return false;
    final checkedCount = checkedChildrenCount;
    return checkedCount > 0 && checkedCount < totalChildrenCount;
  }

  /// 检查是否所有子节点都被选中
  bool get hasAllChildrenChecked {
    if (children.isEmpty) return false;
    return checkedChildrenCount == totalChildrenCount;
  }

  /// 复制节点状态（用于状态管理）
  void copyStateFrom(EmployeeTreeNode other) {
    isExpanded = other.isExpanded;
    isSelected = other.isSelected;
    isChecked = other.isChecked;
    isIndeterminate = other.isIndeterminate;
    isVisible = other.isVisible;
    isDisabled = other.isDisabled;
    clearCache();
  }
}

/// 人员树状选择器组件
class EmployeeTree extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(EmployeeTreeModel employee)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(EmployeeTreeModel employee, bool isSelected)? onNodeSelected;

  /// 搜索查询字符串
  final String? searchQuery;

  /// 数据加载完成回调
  final VoidCallback? onDataLoaded;

  /// 禁用选择的员工ID列表
  final List<String>? disabledIdList;

  const EmployeeTree({
    super.key,
    this.showCheckbox = false,
    this.onNodeTap,
    this.onNodeSelected,
    this.searchQuery,
    this.onDataLoaded,
    this.disabledIdList,
  });

  @override
  State<EmployeeTree> createState() => EmployeeTreeState();
}

// 将 State 类改为公开，以便外部可以访问
class EmployeeTreeState extends State<EmployeeTree> {
  List<EmployeeTreeModel> _list = [];
  final List<EmployeeTreeNode> _treeNodes = [];
  String? _currentSearchQuery;

  // 性能优化：缓存节点映射
  final Map<String, EmployeeTreeNode> _nodeMap = {};

  // 加载状态管理
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _currentSearchQuery = widget.searchQuery;
    _loadEmployeeData();
  }

  @override
  void didUpdateWidget(EmployeeTree oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当搜索查询发生变化时，更新搜索结果
    if (widget.searchQuery != _currentSearchQuery) {
      _currentSearchQuery = widget.searchQuery;
      _applySearchFilter();
    }
  }

  @override
  void dispose() {
    // 清理资源
    _nodeMap.clear();
    super.dispose();
  }

  /// 刷新人员列表数据 - 公开方法供外部调用
  void refresh() {
    _loadEmployeeData();
  }

  /// 展开所有顶级部门节点 - 公开方法供外部调用
  void expandTopLevelDepartments() {
    _expandTopLevelDepartments();
    setState(() {});
  }

  /// 展开指定节点
  void expandNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _nodeMap[nodeId]!.isExpanded = true;
      setState(() {});
    }
  }

  /// 收起指定节点
  void collapseNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _nodeMap[nodeId]!.isExpanded = false;
      setState(() {});
    }
  }

  /// 加载人员数据
  Future<void> _loadEmployeeData() async {
    if (_isLoading) return; // 防止重复加载

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final res = await EmployeeApi.getTreeList();
      if (mounted) {
        setState(() {
          _list = EmployeeTreeModel.fromJsonList(res);
          _buildTreeStructure();
          _applySearchFilter(); // 构建树结构后应用搜索过滤
          _isLoading = false;
        });
        // 数据加载完成后调用回调
        widget.onDataLoaded?.call();
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _errorMessage = '${_EmployeeTreeConstants.loadErrorPrefix}$error';
          _isLoading = false;
        });
      }
    }
  }

  /// 构建树结构
  void _buildTreeStructure() {
    _nodeMap.clear();
    _treeNodes.clear();

    // 从扁平数据构建树形结构
    _buildTreeFromFlatData();
    // 默认展开最顶级的部门节点
    _expandTopLevelDepartments();
  }

  /// 从扁平数据构建树形结构
  void _buildTreeFromFlatData() {
    // 创建所有节点的映射
    for (final employee in _list) {
      final isDisabled = widget.disabledIdList?.contains(employee.id) ?? false;
      _nodeMap[employee.id!] = EmployeeTreeNode(employee: employee, isDisabled: isDisabled);
    }

    // 构建父子关系
    for (final node in _nodeMap.values) {
      if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
        final parent = _nodeMap[node.employee.parentId!]!;
        parent.children.add(node);
      } else {
        // 顶级节点（部门）
        if (node.employee.isDepartment) {
          _treeNodes.add(node);
        }
      }
    }

    // 对子节点进行排序：部门在前，人员在后
    for (final node in _nodeMap.values) {
      node.children.sort((a, b) {
        if (a.employee.isDepartment && !b.employee.isDepartment) return -1;
        if (!a.employee.isDepartment && b.employee.isDepartment) return 1;
        return a.employee.name.compareTo(b.employee.name);
      });
    }

    // 对顶级节点排序
    _treeNodes.sort((a, b) => a.employee.name.compareTo(b.employee.name));
  }

  /// 默认展开最顶级的部门节点
  void _expandTopLevelDepartments() {
    for (final node in _treeNodes) {
      if (node.employee.isDepartment) {
        node.isExpanded = true;
      }
    }
  }

  /// 应用搜索过滤
  void _applySearchFilter() {
    if (_currentSearchQuery == null || _currentSearchQuery!.isEmpty) {
      // 如果没有搜索查询，显示所有节点并重新展开顶级部门
      _setAllNodesVisible(true);
      _expandTopLevelDepartments();
      setState(() {});
      return;
    }

    final query = _currentSearchQuery!.toLowerCase();
    _setAllNodesVisible(false);

    // 标记匹配的节点为可见，并展开其祖先节点
    for (final node in _nodeMap.values) {
      if (node.employee.name.toLowerCase().contains(query)) {
        _setNodeAndAncestorsVisible(node);
        _expandAncestorNodes(node);
      }
    }

    setState(() {});
  }

  /// 设置所有节点的可见性
  void _setAllNodesVisible(bool visible) {
    for (final node in _nodeMap.values) {
      node.isVisible = visible;
    }
  }

  /// 设置节点及其祖先节点为可见
  void _setNodeAndAncestorsVisible(EmployeeTreeNode node) {
    node.isVisible = true;
    if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
      _setNodeAndAncestorsVisible(_nodeMap[node.employee.parentId!]!);
    }
  }

  /// 展开节点的所有祖先节点（用于搜索时显示匹配结果）
  void _expandAncestorNodes(EmployeeTreeNode node) {
    if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
      final parent = _nodeMap[node.employee.parentId!]!;
      parent.isExpanded = true;
      _expandAncestorNodes(parent);
    }
  }

  /// 设置搜索查询
  void setSearchQuery(String query) {
    // 处理空白字符，将其视为空搜索
    _currentSearchQuery = query.trim().isEmpty ? null : query.trim();
    _applySearchFilter();
  }

  /// 获取所有选中的人员
  List<EmployeeTreeModel> getAllCheckedEmployees() {
    final List<EmployeeTreeModel> checkedEmployees = [];
    for (final node in _nodeMap.values) {
      if (node.isChecked && node.employee.isEmployee) {
        checkedEmployees.add(node.employee);
      }
    }
    return checkedEmployees;
  }

  /// 选中节点
  void checkNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _setNodeChecked(_nodeMap[nodeId]!, true);
    }
  }

  /// 取消选中节点
  void uncheckNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _setNodeChecked(_nodeMap[nodeId]!, false);
    }
  }

  /// 强制选中节点（忽略禁用状态，用于初始化）
  void forceCheckNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _forceSetNodeChecked(_nodeMap[nodeId]!, true);
    }
  }

  /// 强制取消选中节点（忽略禁用状态，用于初始化）
  void forceUncheckNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _forceSetNodeChecked(_nodeMap[nodeId]!, false);
    }
  }

  /// 重置所有节点的选中状态
  void resetAllNodesCheck() {
    for (final node in _nodeMap.values) {
      // 如果节点被禁用，跳过不处理，保持当前状态
      if (node.isDisabled) {
        continue;
      }
      node.isChecked = false;
      node.isIndeterminate = false;
      node.clearCache();
    }
    setState(() {});
  }

  /// 设置节点选中状态
  void _setNodeChecked(EmployeeTreeNode node, bool checked) {
    // 如果节点被禁用，不允许改变选中状态
    if (node.isDisabled) {
      return;
    }

    node.isChecked = checked;
    node.isIndeterminate = false;

    // 如果是部门节点，递归选中/取消选中所有子节点（包括子部门和人员）
    if (node.employee.isDepartment) {
      _setAllChildrenChecked(node, checked);
    }

    // 更新父节点状态
    _updateParentNodeState(node);

    setState(() {});

    // 触发选择回调
    widget.onNodeSelected?.call(node.employee, checked);
  }

  /// 强制设置节点选中状态（忽略禁用状态，用于初始化）
  void _forceSetNodeChecked(EmployeeTreeNode node, bool checked) {
    node.isChecked = checked;
    node.isIndeterminate = false;

    // 如果是部门节点，递归选中/取消选中所有子节点（包括子部门和人员）
    if (node.employee.isDepartment) {
      _forceSetAllChildrenChecked(node, checked);
    }

    // 更新父节点状态
    _updateParentNodeState(node);

    setState(() {});

    // 触发选择回调
    widget.onNodeSelected?.call(node.employee, checked);
  }

  /// 递归设置所有子节点的选中状态
  void _setAllChildrenChecked(EmployeeTreeNode parentNode, bool checked) {
    for (final child in parentNode.children) {
      // 如果子节点被禁用，跳过不处理
      if (child.isDisabled) {
        continue;
      }

      child.isChecked = checked;
      child.isIndeterminate = false;
      child.clearCache();

      // 如果子节点也是部门，递归处理其子节点
      if (child.employee.isDepartment) {
        _setAllChildrenChecked(child, checked);
      }
    }
  }

  /// 强制递归设置所有子节点的选中状态（忽略禁用状态）
  void _forceSetAllChildrenChecked(EmployeeTreeNode parentNode, bool checked) {
    for (final child in parentNode.children) {
      child.isChecked = checked;
      child.isIndeterminate = false;
      child.clearCache();

      // 如果子节点也是部门，递归处理其子节点
      if (child.employee.isDepartment) {
        _forceSetAllChildrenChecked(child, checked);
      }
    }
  }

  /// 更新父节点状态
  void _updateParentNodeState(EmployeeTreeNode node) {
    if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
      final parent = _nodeMap[node.employee.parentId!]!;
      parent.clearCache();

      // 计算父节点的选中状态
      _calculateParentCheckState(parent);

      // 递归更新上级父节点
      _updateParentNodeState(parent);
    }
  }

  /// 计算父节点的选中状态
  void _calculateParentCheckState(EmployeeTreeNode parentNode) {
    if (parentNode.children.isEmpty) {
      parentNode.isChecked = false;
      parentNode.isIndeterminate = false;
      return;
    }

    int checkedCount = 0;
    int indeterminateCount = 0;
    int totalCount = parentNode.children.length;

    for (final child in parentNode.children) {
      if (child.isChecked) {
        checkedCount++;
      } else if (child.isIndeterminate) {
        indeterminateCount++;
      }
    }

    // 确定父节点的状态
    if (checkedCount == totalCount) {
      // 所有子节点都被选中
      parentNode.isChecked = true;
      parentNode.isIndeterminate = false;
    } else if (checkedCount == 0 && indeterminateCount == 0) {
      // 没有子节点被选中或处于半选状态
      parentNode.isChecked = false;
      parentNode.isIndeterminate = false;
    } else {
      // 部分子节点被选中或有半选状态
      parentNode.isChecked = false;
      parentNode.isIndeterminate = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              _EmployeeTreeConstants.loadingMessage,
              style: EmployeeTreeStyles.loadingTextStyle(context),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_errorMessage!, style: EmployeeTreeStyles.errorTextStyle(context)),
            const SizedBox(height: 16),
            AppButton(text: _EmployeeTreeConstants.retryButtonText, onPressed: _loadEmployeeData),
          ],
        ),
      );
    }

    if (_treeNodes.isEmpty) {
      return Center(
        child: Text(
          _EmployeeTreeConstants.emptyDataMessage,
          style: EmployeeTreeStyles.emptyDataTextStyle(context),
        ),
      );
    }

    return ListView.builder(
      itemCount: _treeNodes.length,
      itemBuilder: (context, index) {
        return _buildTreeNode(_treeNodes[index]);
      },
    );
  }

  /// 构建树节点
  Widget _buildTreeNode(EmployeeTreeNode node) {
    if (!node.isVisible) return const SizedBox.shrink();

    return Column(
      children: [
        _buildNodeItem(node),
        if (node.isExpanded && node.children.isNotEmpty)
          ...node.children.map(
            (child) =>
                Padding(padding: EmployeeTreeStyles.childNodeIndent, child: _buildTreeNode(child)),
          ),
      ],
    );
  }

  /// 构建节点项
  Widget _buildNodeItem(EmployeeTreeNode node) {
    return Container(
      padding: EmployeeTreeStyles.nodeContainerPadding,
      child: Row(
        children: [
          // 展开/收起图标
          if (node.children.isNotEmpty)
            GestureDetector(
              onTap: () {
                setState(() {
                  node.isExpanded = !node.isExpanded;
                });
              },
              child: AnimatedRotation(
                turns: EmployeeTreeStyles.getExpandIconRotation(node.isExpanded),
                duration: EmployeeTreeStyles.expandAnimationDuration,
                child: Icon(
                  EmployeeTreeStyles.expandIcon,
                  size: EmployeeTreeStyles.expandIconSize,
                  color: EmployeeTreeStyles.expandIconColor(context),
                ),
              ),
            )
          else
            SizedBox(width: EmployeeTreeStyles.expandIconAreaWidth),

          // 复选框
          if (widget.showCheckbox)
            Checkbox(
              value: node.isIndeterminate ? null : node.isChecked,
              tristate: true,
              // 禁用的节点不能操作，但仍然显示选中状态
              onChanged:
                  node.isDisabled
                      ? null
                      : (value) {
                        // 当点击半选状态的复选框时，设置为选中状态
                        if (node.isIndeterminate) {
                          _setNodeChecked(node, true);
                        } else {
                          _setNodeChecked(node, value ?? false);
                        }
                      },
            ),

          // 头像和图标
          if (node.employee.isEmployee) ...[
            ImageCacheUtil.cachedAvatarImage(imageUrl: node.employee.avatar, size: 20),
            SizedBox(width: EmployeeTreeStyles.personIconTextSpacing),
          ],

          // 名称
          Expanded(
            child: GestureDetector(
              onTap: () {
                widget.onNodeTap?.call(node.employee);
              },
              child: Text(
                node.employee.name,
                style: EmployeeTreeStyles.getNodeTextStyle(
                  context,
                  isDepartment: node.employee.isDepartment,
                  isSelected: node.isSelected,
                  isDisabled: node.isDisabled,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
