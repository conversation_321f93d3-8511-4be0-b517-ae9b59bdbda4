import 'package:flutter/cupertino.dart';
import 'package:octasync_client/components/data/app_table/src/app_date.dart';
import 'package:octasync_client/components/data/app_table/src/enums/cell_state_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_common.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_number_format.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_date.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_percentage.dart';
import 'package:intl/intl.dart';

class DateCell extends StatefulWidget {
  const DateCell({
    super.key,
    this.state = CellStateEnum.normal,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final CellStateEnum state;

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;

  final String rowId;
  final String columnId;

  @override
  State<DateCell> createState() => _DateCellState();
}

class _DateCellState extends State<DateCell> {
  late AppTableColumn column;
  late AppTableColumnTypeDate columnObj;
  late Map<String, dynamic> rowData;
  late String rowId;
  late String columnId;

  @override
  void initState() {
    super.initState();
    column = widget.column;
    columnObj = column.type as AppTableColumnTypeDate;
    rowData = widget.rowData;
    rowId = widget.rowId;
    columnId = widget.columnId;
  }

  @override
  Widget build(BuildContext context) {
    var value = rowData[column.field];

    DateTime? dateTime;

    if (value is DateTime) {
      dateTime = value;
    } else if (value is String) {
      dateTime = DateTime.tryParse(value);
    } else if (value == null) {
      dateTime = null;
    }

    String _format =
        AppTableCommon.getDateFormatOption(columnObj.dateType).text +
        (columnObj.showTime ? ' HH:mm' : '');

    if (value != null) {
      var state = widget.state;
      if (state == CellStateEnum.edit) {
        return AppDate(
          initialValue: dateTime,
          format: _format,
          onChanged: (value) {
            rowData[column.field] = value.toString();
          },
        );
      } else {
        if (dateTime != null) {
          var str = columnObj.dateFormat.format(dateTime!).toString();
          return Text(str);
        } else {
          return Text('');
        }
      }
    } else {
      // 处理空值或无效值的情况
      return Text('');
    }
  }
}
