// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role_tree_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoleTreeModel _$RoleTreeModelFromJson(Map<String, dynamic> json) =>
    RoleTreeModel(
      id: json['Id'] as String?,
      name: json['Name'] as String? ?? '',
      parentId: json['ParentId'] as String?,
      parentName: json['ParentName'] as String? ?? '',
      type: (json['Type'] as num?)?.toInt() ?? 1,
      children:
          (json['Children'] as List<dynamic>?)
              ?.map((e) => RoleTreeModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$RoleTreeModelToJson(RoleTreeModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'Id': value,
      'Name': instance.name,
      if (instance.parentId case final value?) 'ParentId': value,
      'ParentName': instance.parentName,
      'Type': instance.type,
      'Children': instance.children.map((e) => e.toJson()).toList(),
    };
