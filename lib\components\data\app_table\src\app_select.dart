import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:octasync_client/components/data/app_table/src/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/src/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/src/model/general_option.dart';
import 'package:octasync_client/components/data/app_table/src/shadow_box.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

double inputHeight = 38;
double contentBorderRadius = 8;
// double selectPanelWidth = 200;
double selectPanelPadding = 10;
double horizontalSpacing = 8;
double verticalSpacing = 0;

var outlineInputBorder = OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1));

typedef ChangedCallback = void Function(dynamic item);

class AppSelect<T> extends StatefulWidget {
  /// 单选还是多选
  // final bool isMultiple;

  /// 单选还是多选
  final SelectEnum subType;

  /// 默认选中value;
  // final T? value;
  // 支持单选 (T) 或多选 (List<T>)
  final dynamic value;

  /// 备选项options
  final List<Object>? options;

  /// 选中回调
  final ChangedCallback? onChanged;

  const AppSelect({
    super.key,
    this.options,
    this.onChanged,
    this.value,
    // this.isMultiple = false, // 默认为单选模式
    this.subType = SelectEnum.single,
  });

  @override
  State<AppSelect<T>> createState() => _AppSelectState<T>();
}

class _AppSelectState<T> extends State<AppSelect<T>> {
  final GlobalKey _textFieldKey = GlobalKey();
  double? _selectPanelWidth;

  late AppCustomTooltipController _selectColumnTypeTooltipController;

  /// 当前传过来的options参数（支持两种类型：GeneralOption、GeneralOptionGroup）
  List<Object> get options => widget.options ?? [];

  // bool get isMultiple => widget.isMultiple;

  bool get isMultiple => widget.subType == SelectEnum.multiple;

  /// 当前options是否分组
  bool get isGroup => options.isNotEmpty && options.first is GeneralOptionGroup;

  /// 当前选中的option（没选中返回null）
  List<GeneralOption> get selectOption {
    var allOptions =
        isGroup
            ? options.expand((g) => (g as GeneralOptionGroup).items).toList()
            : options.cast<GeneralOption>();

    if (isMultiple) {
      return allOptions.where((c) => selectedValueList.contains(c.id)).toList();
    } else {
      var result = allOptions.firstWhereOrNull((item) => item.id == selectedValue);
      if (result != null) {
        return [result];
      } else {
        return [];
      }
    }
  }

  /// 鼠标移上去的项id
  dynamic hoverId = null;

  // 选中值
  late List<T> selectedValueList = []; // 用于多选模式
  late T? selectedValue; // 用于单选模式

  @override
  void initState() {
    super.initState();

    _selectColumnTypeTooltipController = AppCustomTooltipController();

    _setValue();

    WidgetsBinding.instance.addPostFrameCallback((_) => _getTextFieldWidth());
  }

  void _getTextFieldWidth() {
    final renderBox = _textFieldKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      setState(() {
        _selectPanelWidth = renderBox.size.width;
      });
    }
  }

  @override
  dispose() {
    super.dispose();
    _selectColumnTypeTooltipController.dispose();
  }

  @override
  void didUpdateWidget(covariant AppSelect<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    _setValue();

    if (widget.key != null) {
      var widgetKey = (widget.key as ValueKey).value;
    }

    // 如果 options 变了，我们需要检查当前的 selectedValue（selectedValueList） 是否还合法
    // 如果不合法，可能需要清空或设置为新的默认值
    var allOptions = _getAllOptions();

    if (isMultiple) {
      var removes = selectedValueList.where((c) => !allOptions.map((cc) => cc.id).contains(c));

      selectedValueList.removeWhere((c) => removes.contains(c));
    } else {
      if (allOptions.firstWhereOrNull((item) => item.id == selectedValue) == null) {
        setState(() {
          // 如果当前选中的 id 在新选项里不存在了，就清空它
          selectedValue = null;
        });
      } else {
        // // 如果只是 options 列表更新了，但当前选中值依然有效，
        // // Flutter 的 build 方法会自动使用新的 options 重建 UI，
        // // 我们甚至不需要显式调用 setState。
        // // 但如果需要执行某些依赖于新 options 的逻辑，可以在这里调用 setState。
        // setState(() {
        //   // 例如，重新计算某些依赖 options 的值
        // });
      }
    }
  }

  void _setValue() {
    //widget.isMultiple
    if (widget.subType == SelectEnum.multiple) {
      selectedValueList = (widget.value as List).cast<T>();
      selectedValue = null;
    } else {
      selectedValue = widget.value as T?;
      selectedValueList = [];
    }
  }

  void _onOptionTapped(T option) {
    setState(() {
      //widget.isMultiple
      if (widget.subType == SelectEnum.multiple) {
        // 多选模式
        if (selectedValueList.contains(option)) {
          selectedValueList.remove(option); // 移除选中
        } else {
          selectedValueList.add(option); // 添加选中
        }

        if (widget.onChanged != null) {
          widget.onChanged!(selectedValueList); // 返回多选集合
        }
      } else {
        // 单选模式
        if (widget.onChanged != null) {
          selectedValue = option;
          widget.onChanged!(selectedValue); // 返回单个值
          _selectColumnTypeTooltipController.toggleTooltip();
        }
      }
    });
  }

  // 提取一个辅助方法来获取所有选项，避免代码重复
  List<GeneralOption> _getAllOptions() {
    var all =
        isGroup
            ? options
                .expand((g) => (g as GeneralOptionGroup).items)
                .where((c) => c.text.isNotEmpty)
                .toList()
            : options.cast<GeneralOption>().where((c) => c.text.isNotEmpty).toList();

    return all;
  }

  Widget _buildTags(List<GeneralOption> items) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4),
        child: Wrap(
          spacing: 8.0, // 子组件之间的水平间距
          runSpacing: 8.0, // 行与行之间的垂直间距
          children: [
            for (int i = 0; i < items.length; i++)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                  border: Border.all(color: Colors.red, width: 1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Text(
                        items[i].text,
                        maxLines: 1, // 限制为单行
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        child: Icon(Icons.close, size: 12),
                        onTap: () {
                          _onOptionTapped(items[i].id);
                        },
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.hasBoundedWidth ? constraints.maxWidth : double.infinity;

        return IntrinsicWidth(
          child: ConstrainedBox(
            constraints: BoxConstraints(minWidth: 0, maxWidth: maxWidth, minHeight: inputHeight),
            child: Container(width: maxWidth, child: _buildSelectContent(context)),
          ),
        );
      },
    );
  }

  Widget _buildSelectContent(BuildContext context) {
    return Container(
      child: SizedBox(
        height: inputHeight,
        child: AppCustomTooltip(
          showArrow: false,
          manual: true,
          controller: _selectColumnTypeTooltipController,
          closeOnOutsideClick: true,
          onVisibleChange: (visible) {
            // setVisiableTime = DateTime.now();
            // addColumnTooltipVisiable = visible;

            // print('object----${visible}');
          },
          contentChild: ShadowBox(
            borderRadius: contentBorderRadius,
            child: Container(
              width: _selectPanelWidth,
              // height: 300,
              padding: EdgeInsets.only(
                top: 0,
                left: selectPanelPadding,
                bottom: selectPanelPadding,
                right: selectPanelPadding,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(contentBorderRadius),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isGroup)
                    ...options.cast<GeneralOptionGroup<T>>().map((group) {
                      final g = group as GeneralOptionGroup;
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 40,
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              border: Border(bottom: BorderSide(color: Colors.grey, width: 1)),
                            ),
                            child: Text(
                              g.text,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                                decoration: TextDecoration.none,
                              ),
                            ),
                          ),
                          _buildOptions(g.items as List<GeneralOption<T>>),
                        ],
                      );
                    }),
                  if (!isGroup) _buildOptions(options as List<GeneralOption<T>>),
                ],
              ),
            ),
          ),
          placement: TooltipPlacement.bottomCenter,
          child: AppBasicTextField(
            key: _textFieldKey,
            decoration: InputDecoration(
              border: outlineInputBorder,
              contentPadding: EdgeInsets.symmetric(horizontal: 10),
              prefixIcon: Container(
                padding: EdgeInsets.only(right: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 10),
                    Icon(Icons.person, size: 18),
                    SizedBox(width: 4),

                    // 单选
                    if (!isMultiple)
                      Expanded(
                        child:
                            selectOption.isNotEmpty
                                ? Text(
                                  selectOption[0].text,
                                  maxLines: 1, // 限制为单行
                                  overflow: TextOverflow.ellipsis,
                                )
                                : Text('请选择'),
                      ),

                    // 多选
                    if (isMultiple)
                      Expanded(
                        child: selectOption.isNotEmpty ? _buildTags(selectOption) : Text('请选择'),
                      ),
                  ],
                ),
              ),
              suffixIcon: Icon(Icons.keyboard_arrow_down, size: 18),
            ),
            readOnly: true,
            onTap: () {
              _selectColumnTypeTooltipController.toggleTooltip();
            },
          ),

          // TextField(
          //   key: _textFieldKey,
          //   decoration: InputDecoration(
          //     border: outlineInputBorder,
          //     contentPadding: EdgeInsets.symmetric(horizontal: 10),
          //     prefixIcon: Container(
          //       padding: EdgeInsets.only(right: 30),
          //       child: Row(
          //         mainAxisSize: MainAxisSize.min,
          //         children: [
          //           SizedBox(width: 10),
          //           Icon(Icons.person, size: 18),
          //           SizedBox(width: 4),

          //           // 单选
          //           if (!isMultiple)
          //             Expanded(
          //               child:
          //                   selectOption.isNotEmpty
          //                       ? Text(
          //                         selectOption[0].text,
          //                         maxLines: 1, // 限制为单行
          //                         overflow: TextOverflow.ellipsis,
          //                       )
          //                       : Text('请选择'),
          //             ),

          //           // 多选
          //           if (isMultiple)
          //             Expanded(
          //               child:
          //                   selectOption.isNotEmpty
          //                       ? _buildTags(selectOption)
          //                       : Text('请选择'),
          //             ),
          //         ],
          //       ),
          //     ),
          //     suffixIcon: Icon(Icons.keyboard_arrow_down, size: 18),
          //   ),

          //   readOnly: true,
          //   onTap: () {
          //     _selectColumnTypeTooltipController.toggleTooltip();
          //   },
          // ),
        ),
      ),
    );
  }

  Widget _buildOptions(List<GeneralOption<T>> options) {
    return StatefulBuilder(
      builder: (context, setInnerState) {
        return Container(
          padding: EdgeInsets.only(top: selectPanelPadding),
          child: ConstrainedBox(
            constraints: BoxConstraints(maxHeight: 300),
            child: Scrollbar(
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: Column(
                  // spacing: horizontalSpacing, // 水平间距
                  // runSpacing: verticalSpacing, // 垂直间距
                  children:
                      options.where((c) => c.text.isNotEmpty).map((item) {
                        return MouseRegion(
                          onHover: (event) {
                            setInnerState(() {
                              hoverId = item.id;
                            });
                          },
                          onExit: (event) {
                            setInnerState(() {
                              hoverId = null;
                            });
                          },
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              _onOptionTapped(item.id);
                              setInnerState(() {});
                            },
                            child: SizedBox(
                              child: Container(
                                height: 36,
                                padding: EdgeInsets.symmetric(horizontal: 5),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.all(Radius.circular(4)),
                                  color: hoverId == item.id ? Colors.grey[300] : Colors.transparent,
                                ),
                                alignment: Alignment.centerLeft, // 垂直居中，水平靠左
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        '${item.text}',
                                        maxLines: 1, // 限制为单行
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                          decoration: TextDecoration.none,
                                        ),
                                      ),
                                    ),

                                    if (item.id == selectedValue ||
                                        selectedValueList.contains(item.id))
                                      Icon(Icons.check, size: 14),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  GeneralOption? firstWhereOrNull(Iterable<GeneralOption> list, bool Function(GeneralOption) test) {
    for (var item in list) {
      if (test(item)) return item;
    }
    return null;
  }
}
