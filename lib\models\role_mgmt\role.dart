import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:octasync_client/components/selector/role_selector/role_tree_model.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/role_auth_var.dart';

part 'role.g.dart';

@JsonSerializable(explicitToJson: true)
class Role {
  @Json<PERSON>ey(name: 'Id')
  String? id;

  @JsonKey(name: 'Name', defaultValue: '')
  String name;

  @JsonKey(name: 'GroupId', defaultValue: '')
  String groupId;

  @JsonKey(name: 'GroupName', defaultValue: '')
  String groupName;

  /// 父角色
  @JsonKey(name: 'ParentIdList', defaultValue: [])
  List<String> parentIdList;

  /// 系统配置 1.是 2.不是
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Defaultenum')
  final int defaultenum;

  /// 数据管理范围（1：全组织；2：本部门及以下部门；3：本部门；4：本人数据；5：指定部门）
  @Json<PERSON>ey(name: 'DataScopeenum', defaultValue: 1)
  int dataScopeenum;

  @JsonKey(name: 'PowereList', defaultValue: [])
  List<int> powereList;

  @JsonKey(name: 'DepartmentIdList', defaultValue: [])
  List<String> departmentIdList;

  @JsonKey(name: 'DepartmentList', defaultValue: <DepartmentModel>[])
  List<DepartmentModel>? departmentList;

  /// 互斥角色
  @JsonKey(name: 'ExclusionIdList', defaultValue: [])
  List<String> exclusionIdList;

  /// 互斥角色对象
  @JsonKey(name: 'ExclusionList', defaultValue: <Role>[])
  List<Role> exclusionList;

  /// 是否显示（用于UI）
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool isVisible;

  Role({
    this.id,
    this.name = '',
    this.groupId = '',
    this.groupName = '',
    this.parentIdList = const [],
    this.powereList = const [],
    this.departmentIdList = const [],
    this.departmentList = const [],
    this.dataScopeenum = 1,
    this.defaultenum = 2,
    this.exclusionIdList = const [],
    this.exclusionList = const [],
    this.isVisible = false,
  });

  /// 数据管理范围枚举值到描述的映射
  static String getDataScopeDescription(int dataScopeEnum) {
    var obj = RoleAuthVar.dataScopeenums.firstWhereOrNull((c) => c.value == dataScopeEnum);
    if (obj == null) {
      return '未知类型';
    }
    return obj.label;
  }

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);

  Map<String, dynamic> toJson() => _$RoleToJson(this);

  /// 转换为 RoleTreeModel（用于员工选择器）
  RoleTreeModel toRoleTreeModel() {
    return RoleTreeModel(
      id: id,
      name: name,
      type: 2, // 2表示员工类型
    );
  }

  /// 从 RoleTreeModel 创建 Role（仅包含基本信息）
  static Role fromRoleTreeModel(RoleTreeModel treeModel) {
    return Role(id: treeModel.id, name: treeModel.name);
  }

  /// 批量转换 Role 列表为 RoleTreeModel 列表
  static List<RoleTreeModel> toRoleTreeModelList(List<Role> employees) {
    return employees.map((employee) => employee.toRoleTreeModel()).toList();
  }

  /// 批量从 RoleTreeModel 列表创建 Role 列表
  static List<Role> fromRoleTreeModelList(List<RoleTreeModel> treeModels) {
    return treeModels
        .where((model) => model.isRole) // 只处理员工类型，过滤部门
        .map((model) => Role.fromRoleTreeModel(model))
        .toList();
  }
}
